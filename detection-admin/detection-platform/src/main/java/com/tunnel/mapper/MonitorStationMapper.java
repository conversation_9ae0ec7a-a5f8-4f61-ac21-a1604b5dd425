package com.tunnel.mapper;

import com.tunnel.domain.MonitorStation;

import java.util.List;

/**
 * 监测站点Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationMapper 
{
    /**
     * 查询监测站点
     * 
     * @param id 监测站点主键
     * @return 监测站点
     */
    public MonitorStation selectMonitorStationById(Long id);

    /**
     * 查询监测站点列表
     * 
     * @param monitorStation 监测站点
     * @return 监测站点集合
     */
    public List<MonitorStation> selectMonitorStationList(MonitorStation monitorStation);

    /**
     * 新增监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int insertMonitorStation(MonitorStation monitorStation);

    /**
     * 修改监测站点
     * 
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int updateMonitorStation(MonitorStation monitorStation);

    /**
     * 删除监测站点
     * 
     * @param id 监测站点主键
     * @return 结果
     */
    public int deleteMonitorStationById(Long id);

    /**
     * 批量删除监测站点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitorStationByIds(Long[] ids);

    List<MonitorStation> selectMonitorStationByType(MonitorStation monitorStation);

    List<MonitorStation> selectAll(MonitorStation monitorStation);
}
