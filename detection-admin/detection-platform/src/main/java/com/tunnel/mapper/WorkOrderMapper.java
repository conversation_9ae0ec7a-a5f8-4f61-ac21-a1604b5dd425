package com.tunnel.mapper;

import com.tunnel.domain.WorkOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护工单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface WorkOrderMapper {
    /**
     * 查询维护工单
     *
     * @param id 维护工单主键
     * @return 维护工单
     */
    public WorkOrder selectWorkOrderById(Long id);

    /**
     * 查询维护工单列表
     *
     * @param workOrder 维护工单
     * @return 维护工单集合
     */
    public List<WorkOrder> selectWorkOrderList(WorkOrder workOrder);

    /**
     * 查询维护工单列表（关联站点和计划信息）
     *
     * @param workOrder 维护工单
     * @return 维护工单集合（包含站点和计划信息）
     */
    public List<WorkOrder> selectWorkOrderListWithDetails(WorkOrder workOrder);

    /**
     * 新增维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    public int insertWorkOrder(WorkOrder workOrder);

    /**
     * 修改维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    public int updateWorkOrder(WorkOrder workOrder);

    /**
     * 删除维护工单
     *
     * @param id 维护工单主键
     * @return 结果
     */
    public int deleteWorkOrderById(Long id);

    /**
     * 批量删除维护工单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderByIds(Long[] ids);

    /**
     * 根据计划ID查询工单列表
     *
     * @param planId 计划ID
     * @return 工单集合
     */
    public List<WorkOrder> selectWorkOrderListByPlanId(@Param("planId") Long planId);

    /**
     * 根据站点ID查询工单列表
     *
     * @param stationId 站点ID
     * @return 工单集合
     */
    public List<WorkOrder> selectWorkOrderListByStationId(@Param("stationId") Long stationId);

    /**
     * 检查计划ID是否存在
     *
     * @param planId 计划ID
     * @return 结果
     */
    public int checkPlanIdExists(@Param("planId") Long planId);

    /**
     * 检查站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int checkStationIdExists(@Param("stationId") Long stationId);

    /**
     * 获取工单记录数
     *
     * @param workOrder 查询条件
     * @return 记录数
     */
    public int countWorkOrder(WorkOrder workOrder);

    /**
     * 根据计划ID和状态查询工单数量
     *
     * @param planId 计划ID
     * @param status 工单状态
     * @return 工单数量
     */
    public int countWorkOrderByPlanIdAndStatus(@Param("planId") Long planId, @Param("status") Integer status);

    /**
     * 批量更新工单状态
     *
     * @param ids 工单ID数组
     * @param status 新状态
     * @param modifier 修改人
     * @return 结果
     */
    public int batchUpdateWorkOrderStatus(@Param("ids") Long[] ids, @Param("status") Integer status, @Param("modifier") Long modifier);

    /**
     * 根据站点编码查找站点ID
     *
     * @param stationCode 站点编码
     * @return 站点ID
     */
    public Long selectStationIdByCode(@Param("stationCode") String stationCode);
}
