package com.tunnel.mapper;

import com.tunnel.domain.Plan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务区/收费站维护计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface PlanMapper {
    /**
     * 查询服务区/收费站维护计划
     *
     * @param id 服务区/收费站维护计划主键
     * @return 服务区/收费站维护计划
     */
    public Plan selectPlanById(Long id);

    /**
     * 查询服务区/收费站维护计划列表
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划集合
     */
    public List<Plan> selectPlanList(Plan plan);

    /**
     * 查询服务区/收费站维护计划列表（关联站点信息）
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划集合（包含站点信息）
     */
    public List<Plan> selectPlanListWithStation(Plan plan);

    /**
     * 新增服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    public int insertPlan(Plan plan);

    /**
     * 修改服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    public int updatePlan(Plan plan);

    /**
     * 删除服务区/收费站维护计划
     *
     * @param id 服务区/收费站维护计划主键
     * @return 结果
     */
    public int deletePlanById(Long id);

    /**
     * 批量删除服务区/收费站维护计划
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePlanByIds(Long[] ids);

    /**
     * 根据站点ID查询维护计划列表
     *
     * @param stationId 站点ID
     * @return 维护计划集合
     */
    public List<Plan> selectPlanListByStationId(@Param("stationId") String stationId);

    /**
     * 根据站点名称查询站点ID
     *
     * @param stationName 站点名称
     * @return 站点ID
     */
    public String selectStationIdByName(@Param("stationName") String stationName);

    /**
     * 检查站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int checkStationIdExists(@Param("stationId") String stationId);

    /**
     * 获取维护计划记录数
     *
     * @param plan 查询条件
     * @return 记录数
     */
    public int countPlan(Plan plan);
}
