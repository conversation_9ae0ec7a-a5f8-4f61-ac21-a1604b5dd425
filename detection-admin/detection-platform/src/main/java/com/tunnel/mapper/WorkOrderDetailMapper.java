package com.tunnel.mapper;

import com.tunnel.domain.WorkOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 维护工单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Mapper
public interface WorkOrderDetailMapper {
    /**
     * 查询维护工单明细
     *
     * @param id 维护工单明细主键
     * @return 维护工单明细
     */
    public WorkOrderDetail selectWorkOrderDetailById(Long id);

    /**
     * 查询维护工单明细列表
     *
     * @param workOrderDetail 维护工单明细
     * @return 维护工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailList(WorkOrderDetail workOrderDetail);

    /**
     * 根据工单ID查询工单明细列表
     *
     * @param orderId 工单ID
     * @return 工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据工单ID查询工单明细列表（按维护类型分组）
     *
     * @param orderId 工单ID
     * @return 工单明细集合（按work_type分组排序）
     */
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderIdGrouped(@Param("orderId") Long orderId);

    /**
     * 根据工单ID数组查询工单明细列表
     *
     * @param orderIds 工单ID数组
     * @return 工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderIds(@Param("orderIds") Long[] orderIds);

    /**
     * 新增维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    public int insertWorkOrderDetail(WorkOrderDetail workOrderDetail);

    /**
     * 批量新增维护工单明细
     *
     * @param workOrderDetailList 工单明细列表
     * @return 结果
     */
    public int batchInsertWorkOrderDetail(@Param("list") List<WorkOrderDetail> workOrderDetailList);

    /**
     * 修改维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    public int updateWorkOrderDetail(WorkOrderDetail workOrderDetail);

    /**
     * 删除维护工单明细
     *
     * @param id 维护工单明细主键
     * @return 结果
     */
    public int deleteWorkOrderDetailById(Long id);

    /**
     * 批量删除维护工单明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderDetailByIds(Long[] ids);

    /**
     * 根据工单ID删除工单明细
     *
     * @param orderId 工单ID
     * @return 结果
     */
    public int deleteWorkOrderDetailByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据工单ID数组删除工单明细
     *
     * @param orderIds 工单ID数组
     * @return 结果
     */
    public int deleteWorkOrderDetailByOrderIds(@Param("orderIds") Long[] orderIds);

    /**
     * 统计工单明细数量
     *
     * @param workOrderDetail 查询条件
     * @return 记录数
     */
    public int countWorkOrderDetail(WorkOrderDetail workOrderDetail);

    /**
     * 根据工单ID统计明细数量
     *
     * @param orderId 工单ID
     * @return 明细数量
     */
    public int countWorkOrderDetailByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据工单ID和维护类型统计明细数量
     *
     * @param orderId 工单ID
     * @param workType 维护类型
     * @return 明细数量
     */
    public int countWorkOrderDetailByOrderIdAndWorkType(@Param("orderId") Long orderId, @Param("workType") String workType);

    /**
     * 获取工单的维护类型列表（去重）
     *
     * @param orderId 工单ID
     * @return 维护类型列表
     */
    public List<String> selectWorkTypeListByOrderId(@Param("orderId") Long orderId);

    /**
     * 检查工单ID是否存在
     *
     * @param orderId 工单ID
     * @return 结果
     */
    public int checkOrderIdExists(@Param("orderId") Long orderId);
}
