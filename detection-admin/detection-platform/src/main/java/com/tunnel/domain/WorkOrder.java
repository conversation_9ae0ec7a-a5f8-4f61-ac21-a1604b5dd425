package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 维护工单对象 sc_work_order
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 服务区/收费站ID */
    @Excel(name = "服务区/收费站ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "服务区/收费站ID不能为空")
    private Long stationId;

    /** 计划表ID */
    @Excel(name = "计划表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "计划表ID不能为空")
    private Long planId;

    /** 工单状态:0.进行中,1.已完成 */
    @Excel(name = "工单状态", readConverterExp = "0=进行中,1=已完成")
    @NotNull(message = "工单状态不能为空")
    private Integer status;

    /** 工单类型:0.普通,1.紧急工单 */
    @Excel(name = "工单类型", readConverterExp = "0=普通,1=紧急工单")
    @NotNull(message = "工单类型不能为空")
    private Integer type;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    // ========== 关联查询字段 ==========
    
    /** 站点编码 */
    @Excel(name = "站点编码", cellType = Excel.ColumnType.STRING)
    private String stationCode;

    /** 站点名称 */
    @Excel(name = "站点名称", cellType = Excel.ColumnType.STRING)
    private String stationName;

    /** 地区 */
    @Excel(name = "地区", cellType = Excel.ColumnType.STRING)
    private String district;

    /** 分公司 */
    @Excel(name = "分公司", cellType = Excel.ColumnType.STRING)
    private String companyName;

    /** 开通情况 */
    @Excel(name = "开通情况", cellType = Excel.ColumnType.STRING)
    private String openingStatus;

    /** 运维分类 */
    @Excel(name = "运维分类", cellType = Excel.ColumnType.STRING)
    private String maintenanceCategory;

    /** 污水处理工艺 */
    @Excel(name = "污水处理工艺", cellType = Excel.ColumnType.STRING)
    private String sewageProcess;

    /** 设备型号 */
    @Excel(name = "设备型号", cellType = Excel.ColumnType.STRING)
    private String equipmentModel;

    /** 设备数量 */
    @Excel(name = "设备数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer equipmentQuantity;

    /** 处理规模（t/d） */
    @Excel(name = "处理规模(t/d)", cellType = Excel.ColumnType.NUMERIC)
    private Double processingScale;

    /** 运维类型 */
    @Excel(name = "运维类型", cellType = Excel.ColumnType.STRING)
    private String maintenanceType;

    /** 污染类型 */
    @Excel(name = "污染类型", cellType = Excel.ColumnType.STRING)
    private String pollutionType;

    /** 站点状态 */
    @Excel(name = "站点状态", readConverterExp = "0=正常,1=异常")
    private Integer stationState;

    /** 故障状态 */
    @Excel(name = "故障状态", readConverterExp = "0=正常,1=故障")
    private Integer faulty;

    // ========== 计划相关字段 ==========
    
    /** 维护开始时间 */
    @Excel(name = "维护开始时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planStartTime;

    /** 维护截止时间 */
    @Excel(name = "维护截止时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    /** 计划备注 */
    @Excel(name = "计划备注", cellType = Excel.ColumnType.STRING)
    private String planRemark;

    // ========== 搜索参数 ==========
    
    /** 站点名称或编码（搜索用） */
    private String stationNameOrCode;

    /** 工单状态数组（搜索用） */
    private Integer[] statusArray;

    /** 工单类型数组（搜索用） */
    private Integer[] typeArray;

    /** 创建时间范围开始 */
    private Date createTimeBegin;

    /** 创建时间范围结束 */
    private Date createTimeEnd;

    /** 计划开始时间范围开始 */
    private Date planStartTimeBegin;

    /** 计划开始时间范围结束 */
    private Date planStartTimeEnd;
}
