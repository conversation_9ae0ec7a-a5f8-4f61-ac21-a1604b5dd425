package com.tunnel.domain.vo;

import com.tunnel.common.annotation.Excel;
import lombok.Data;

/**
 * 服务区/收费站维护计划导入VO
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class PlanImportVO {
    
    /** 维护时间 */
    @Excel(name = "维护时间", cellType = Excel.ColumnType.STRING)
    private String maintenanceTime;

    /** 服务区 */
    @Excel(name = "服务区", cellType = Excel.ColumnType.STRING)
    private String stationName;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    private String remark;
}
