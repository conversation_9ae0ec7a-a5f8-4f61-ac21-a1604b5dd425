package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 服务区/收费站维护计划对象 sc_plan
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Plan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 服务区/收费站ID */
    @Excel(name = "服务区/收费站ID", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "服务区/收费站ID不能为空")
    @Size(max = 50, message = "服务区/收费站ID长度不能超过50个字符")
    private String stationId;

    /** 维护开始时间 */
    @Excel(name = "维护开始时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "维护开始时间不能为空")
    private Date startTime;

    /** 维护截止时间 */
    @Excel(name = "维护截止时间", cellType = Excel.ColumnType.STRING, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "维护截止时间不能为空")
    private Date endTime;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    // ========== 关联查询字段 ==========
    
    /** 站点编码 */
    @Excel(name = "站点编码", cellType = Excel.ColumnType.STRING)
    private String stationCode;

    /** 站点名称 */
    @Excel(name = "站点名称", cellType = Excel.ColumnType.STRING)
    private String stationName;

    /** 地区 */
    @Excel(name = "地区", cellType = Excel.ColumnType.STRING)
    private String district;

    /** 分公司 */
    @Excel(name = "分公司", cellType = Excel.ColumnType.STRING)
    private String companyName;

    /** 经度 */
    @Excel(name = "经度", cellType = Excel.ColumnType.NUMERIC)
    private Double lat;

    /** 纬度 */
    @Excel(name = "纬度", cellType = Excel.ColumnType.NUMERIC)
    private Double lon;

    /** 开通情况 */
    @Excel(name = "开通情况", cellType = Excel.ColumnType.STRING)
    private String openingStatus;

    /** 运维分类 */
    @Excel(name = "运维分类", cellType = Excel.ColumnType.STRING)
    private String maintenanceCategory;

    /** 污水处理工艺 */
    @Excel(name = "污水处理工艺", cellType = Excel.ColumnType.STRING)
    private String sewageProcess;

    /** 设备型号 */
    @Excel(name = "设备型号", cellType = Excel.ColumnType.STRING)
    private String equipmentModel;

    /** 设备数量 */
    @Excel(name = "设备数量", cellType = Excel.ColumnType.NUMERIC)
    private Integer equipmentQuantity;

    /** 处理规模（t/d） */
    @Excel(name = "处理规模(t/d)", cellType = Excel.ColumnType.NUMERIC)
    private Double processingScale;

    /** 运维类型 */
    @Excel(name = "运维类型", cellType = Excel.ColumnType.STRING)
    private String maintenanceType;

    /** 污染类型 */
    @Excel(name = "污染类型", cellType = Excel.ColumnType.STRING)
    private String pollutionType;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    private Integer state;

    /** 故障状态 */
    @Excel(name = "故障状态", readConverterExp = "0=正常,1=故障")
    private Integer faulty;

    // ========== 搜索参数 ==========

    /** 站点名称或编码（搜索用） */
    private String stationNameOrCode;

    /** 维护时间范围开始 */
    private Date startTimeBegin;

    /** 维护时间范围结束 */
    private Date startTimeEnd;

    /** 维护截止时间范围开始 */
    private Date endTimeBegin;

    /** 维护截止时间范围结束 */
    private Date endTimeEnd;
}
