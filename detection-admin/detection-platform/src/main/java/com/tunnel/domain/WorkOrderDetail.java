package com.tunnel.domain;

import com.tunnel.common.annotation.Excel;
import com.tunnel.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 维护工单明细对象 sc_work_order_detail
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderDetail extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 唯一主键 */
    private Long id;

    /** 工单ID */
    @Excel(name = "工单ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "工单ID不能为空")
    private Long orderId;

    /** 维护类型 */
    @Excel(name = "维护类型", cellType = Excel.ColumnType.STRING)
    @NotBlank(message = "维护类型不能为空")
    @Size(max = 50, message = "维护类型长度不能超过50个字符")
    private String workType;

    /** 维护名称项 */
    @Excel(name = "维护名称项", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "维护名称项不能为空")
    private String workTitle;

    /** 经度 */
    @Excel(name = "经度", cellType = Excel.ColumnType.STRING)
    @Size(max = 255, message = "经度长度不能超过255个字符")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度", cellType = Excel.ColumnType.STRING)
    @Size(max = 255, message = "纬度长度不能超过255个字符")
    private String latitude;

    /** 照片路径 */
    @Excel(name = "照片路径", cellType = Excel.ColumnType.STRING)
    @NotNull(message = "照片路径不能为空")
    private String picUrl;

    /** 备注 */
    @Excel(name = "备注", cellType = Excel.ColumnType.STRING)
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 创建人 */
    private Long creator;

    /** 更新人 */
    private Long modifier;

    // ========== 关联查询字段 ==========
    
    /** 工单编号（关联查询） */
    @Excel(name = "工单编号", cellType = Excel.ColumnType.NUMERIC)
    private Long workOrderId;

    /** 工单状态（关联查询） */
    @Excel(name = "工单状态", readConverterExp = "0=进行中,1=已完成")
    private Integer workOrderStatus;

    /** 工单类型（关联查询） */
    @Excel(name = "工单类型", readConverterExp = "0=普通,1=紧急工单")
    private Integer workOrderType;

    /** 站点编码（关联查询） */
    @Excel(name = "站点编码", cellType = Excel.ColumnType.STRING)
    private String stationCode;

    /** 站点名称（关联查询） */
    @Excel(name = "站点名称", cellType = Excel.ColumnType.STRING)
    private String stationName;

    // ========== 搜索参数 ==========
    
    /** 工单ID数组（搜索用） */
    private Long[] orderIdArray;

    /** 维护类型数组（搜索用） */
    private String[] workTypeArray;

    /** 维护名称项数组（搜索用） */
    private Integer[] workTitleArray;

    /** 创建时间范围开始 */
    private Date createTimeBegin;

    /** 创建时间范围结束 */
    private Date createTimeEnd;

    // ========== 业务扩展字段 ==========
    
    /** 照片URL列表（用于前端显示多张照片） */
    private String[] picUrlList;

    /** 维护类型名称（用于前端显示） */
    private String workTypeName;

    /** 维护名称项名称（用于前端显示） */
    private String workTitleName;

    /** 位置信息（经纬度组合显示） */
    private String location;

    /** 是否有位置信息 */
    private Boolean hasLocation;

    /** 照片数量 */
    private Integer picCount;

    /** 同组详情数量（按work_type分组） */
    private Integer groupCount;

    /** 分组排序（用于前端分组显示） */
    private Integer groupOrder;
}
