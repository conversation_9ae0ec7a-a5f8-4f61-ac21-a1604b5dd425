package com.tunnel.service;

import com.tunnel.domain.WorkOrder;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 维护工单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface WorkOrderService {
    /**
     * 查询维护工单
     *
     * @param id 维护工单主键
     * @return 维护工单
     */
    public WorkOrder selectWorkOrderById(Long id);

    /**
     * 查询维护工单列表
     *
     * @param workOrder 维护工单
     * @return 维护工单集合
     */
    public List<WorkOrder> selectWorkOrderList(WorkOrder workOrder);

    /**
     * 查询维护工单列表（关联站点和计划信息）
     *
     * @param workOrder 维护工单
     * @return 维护工单集合（包含站点和计划信息）
     */
    public List<WorkOrder> selectWorkOrderListWithDetails(WorkOrder workOrder);

    /**
     * 新增维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    public int insertWorkOrder(WorkOrder workOrder);

    /**
     * 修改维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    public int updateWorkOrder(WorkOrder workOrder);

    /**
     * 批量删除维护工单
     *
     * @param ids 需要删除的维护工单主键集合
     * @return 结果
     */
    public int deleteWorkOrderByIds(Long[] ids);

    /**
     * 删除维护工单信息
     *
     * @param id 维护工单主键
     * @return 结果
     */
    public int deleteWorkOrderById(Long id);

    /**
     * 根据计划ID查询工单列表
     *
     * @param planId 计划ID
     * @return 工单集合
     */
    public List<WorkOrder> selectWorkOrderListByPlanId(Long planId);

    /**
     * 根据站点ID查询工单列表
     *
     * @param stationId 站点ID
     * @return 工单集合
     */
    public List<WorkOrder> selectWorkOrderListByStationId(Long stationId);

    /**
     * 导出维护工单列表
     *
     * @param response HTTP响应
     * @param workOrder 查询条件
     */
    public void exportWorkOrder(HttpServletResponse response, WorkOrder workOrder);

    /**
     * 校验计划ID是否存在
     *
     * @param planId 计划ID
     * @return 结果
     */
    public boolean checkPlanIdExists(Long planId);

    /**
     * 校验站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public boolean checkStationIdExists(Long stationId);

    /**
     * 根据计划ID和状态查询工单数量
     *
     * @param planId 计划ID
     * @param status 工单状态
     * @return 工单数量
     */
    public int countWorkOrderByPlanIdAndStatus(Long planId, Integer status);

    /**
     * 批量更新工单状态
     *
     * @param ids 工单ID数组
     * @param status 新状态
     * @return 结果
     */
    public int batchUpdateWorkOrderStatus(Long[] ids, Integer status);

    /**
     * 完成工单
     *
     * @param id 工单ID
     * @return 结果
     */
    public int completeWorkOrder(Long id);

    /**
     * 重新开始工单
     *
     * @param id 工单ID
     * @return 结果
     */
    public int restartWorkOrder(Long id);

    /**
     * 根据计划创建工单
     *
     * @param planId 计划ID
     * @return 结果
     */
    public int createWorkOrderByPlan(Long planId);
}
