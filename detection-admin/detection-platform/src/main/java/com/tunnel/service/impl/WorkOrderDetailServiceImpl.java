package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.WorkOrder;
import com.tunnel.domain.WorkOrderDetail;
import com.tunnel.mapper.WorkOrderDetailMapper;
import com.tunnel.mapper.WorkOrderMapper;
import com.tunnel.service.WorkOrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维护工单明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class WorkOrderDetailServiceImpl implements WorkOrderDetailService {

    @Autowired
    private WorkOrderDetailMapper workOrderDetailMapper;

    @Autowired
    private WorkOrderMapper workOrderMapper;

    /**
     * 查询维护工单明细
     *
     * @param id 维护工单明细主键
     * @return 维护工单明细
     */
    @Override
    public WorkOrderDetail selectWorkOrderDetailById(Long id) {
        return workOrderDetailMapper.selectWorkOrderDetailById(id);
    }

    /**
     * 查询维护工单明细列表
     *
     * @param workOrderDetail 维护工单明细
     * @return 维护工单明细
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailList(WorkOrderDetail workOrderDetail) {
        return workOrderDetailMapper.selectWorkOrderDetailList(workOrderDetail);
    }

    /**
     * 根据工单ID查询工单明细列表
     *
     * @param orderId 工单ID
     * @return 工单明细集合
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderId(Long orderId) {
        return workOrderDetailMapper.selectWorkOrderDetailListByOrderId(orderId);
    }

    /**
     * 根据工单ID查询工单明细列表（按维护类型分组）
     *
     * @param orderId 工单ID
     * @return 按维护类型分组的工单明细Map，key为维护类型，value为明细列表
     */
    @Override
    public Map<String, List<WorkOrderDetail>> selectWorkOrderDetailListByOrderIdGrouped(Long orderId) {
        List<WorkOrderDetail> detailList = workOrderDetailMapper.selectWorkOrderDetailListByOrderIdGrouped(orderId);
        
        // 按维护类型分组
        Map<String, List<WorkOrderDetail>> groupedMap = detailList.stream()
                .collect(Collectors.groupingBy(WorkOrderDetail::getWorkType, LinkedHashMap::new, Collectors.toList()));
        
        return groupedMap;
    }

    /**
     * 根据工单ID数组查询工单明细列表
     *
     * @param orderIds 工单ID数组
     * @return 工单明细集合
     */
    @Override
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderIds(Long[] orderIds) {
        return workOrderDetailMapper.selectWorkOrderDetailListByOrderIds(orderIds);
    }

    /**
     * 新增维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWorkOrderDetail(WorkOrderDetail workOrderDetail) {
        // 校验工单ID是否存在
        if (!checkOrderIdExists(workOrderDetail.getOrderId())) {
            throw new ServiceException("工单ID不存在：" + workOrderDetail.getOrderId());
        }

        workOrderDetail.setCreateTime(DateUtils.getNowDate());
        workOrderDetail.setCreator(SecurityUtils.getUserId());
        return workOrderDetailMapper.insertWorkOrderDetail(workOrderDetail);
    }

    /**
     * 批量新增维护工单明细
     *
     * @param workOrderDetailList 工单明细列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertWorkOrderDetail(List<WorkOrderDetail> workOrderDetailList) {
        if (workOrderDetailList == null || workOrderDetailList.isEmpty()) {
            return 0;
        }

        // 校验工单ID是否存在
        Set<Long> orderIds = workOrderDetailList.stream()
                .map(WorkOrderDetail::getOrderId)
                .collect(Collectors.toSet());
        
        for (Long orderId : orderIds) {
            if (!checkOrderIdExists(orderId)) {
                throw new ServiceException("工单ID不存在：" + orderId);
            }
        }

        // 设置创建信息
        Long userId = SecurityUtils.getUserId();
        Date now = DateUtils.getNowDate();
        workOrderDetailList.forEach(detail -> {
            detail.setCreator(userId);
            detail.setCreateTime(now);
        });

        return workOrderDetailMapper.batchInsertWorkOrderDetail(workOrderDetailList);
    }

    /**
     * 修改维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWorkOrderDetail(WorkOrderDetail workOrderDetail) {
        // 校验工单ID是否存在
        if (workOrderDetail.getOrderId() != null && !checkOrderIdExists(workOrderDetail.getOrderId())) {
            throw new ServiceException("工单ID不存在：" + workOrderDetail.getOrderId());
        }

        workOrderDetail.setModifier(SecurityUtils.getUserId());
        return workOrderDetailMapper.updateWorkOrderDetail(workOrderDetail);
    }

    /**
     * 批量删除维护工单明细
     *
     * @param ids 需要删除的维护工单明细主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByIds(Long[] ids) {
        return workOrderDetailMapper.deleteWorkOrderDetailByIds(ids);
    }

    /**
     * 删除维护工单明细信息
     *
     * @param id 维护工单明细主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailById(Long id) {
        return workOrderDetailMapper.deleteWorkOrderDetailById(id);
    }

    /**
     * 根据工单ID删除工单明细
     *
     * @param orderId 工单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByOrderId(Long orderId) {
        return workOrderDetailMapper.deleteWorkOrderDetailByOrderId(orderId);
    }

    /**
     * 根据工单ID数组删除工单明细
     *
     * @param orderIds 工单ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderDetailByOrderIds(Long[] orderIds) {
        return workOrderDetailMapper.deleteWorkOrderDetailByOrderIds(orderIds);
    }

    /**
     * 导出维护工单明细列表
     *
     * @param response HTTP响应
     * @param workOrderDetail 查询条件
     */
    @Override
    public void exportWorkOrderDetail(HttpServletResponse response, WorkOrderDetail workOrderDetail) {
        List<WorkOrderDetail> list = workOrderDetailMapper.selectWorkOrderDetailList(workOrderDetail);
        ExcelUtil<WorkOrderDetail> util = new ExcelUtil<WorkOrderDetail>(WorkOrderDetail.class);
        util.exportExcel(response, list, "维护工单明细数据");
    }

    /**
     * 根据工单ID统计明细数量
     *
     * @param orderId 工单ID
     * @return 明细数量
     */
    @Override
    public int countWorkOrderDetailByOrderId(Long orderId) {
        return workOrderDetailMapper.countWorkOrderDetailByOrderId(orderId);
    }

    /**
     * 根据工单ID和维护类型统计明细数量
     *
     * @param orderId 工单ID
     * @param workType 维护类型
     * @return 明细数量
     */
    @Override
    public int countWorkOrderDetailByOrderIdAndWorkType(Long orderId, String workType) {
        return workOrderDetailMapper.countWorkOrderDetailByOrderIdAndWorkType(orderId, workType);
    }

    /**
     * 获取工单的维护类型列表（去重）
     *
     * @param orderId 工单ID
     * @return 维护类型列表
     */
    @Override
    public List<String> selectWorkTypeListByOrderId(Long orderId) {
        return workOrderDetailMapper.selectWorkTypeListByOrderId(orderId);
    }

    /**
     * 检查工单ID是否存在
     *
     * @param orderId 工单ID
     * @return 结果
     */
    @Override
    public boolean checkOrderIdExists(Long orderId) {
        if (orderId == null) {
            return false;
        }
        return workOrderDetailMapper.checkOrderIdExists(orderId) > 0;
    }

    /**
     * 获取工单详情统计信息
     *
     * @param orderId 工单ID
     * @return 统计信息Map
     */
    @Override
    public Map<String, Object> getWorkOrderDetailStatistics(Long orderId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总明细数量
        int totalCount = countWorkOrderDetailByOrderId(orderId);
        statistics.put("totalCount", totalCount);
        
        // 维护类型列表
        List<String> workTypeList = selectWorkTypeListByOrderId(orderId);
        statistics.put("workTypeList", workTypeList);
        statistics.put("workTypeCount", workTypeList.size());
        
        // 按维护类型统计数量
        Map<String, Integer> workTypeCountMap = new HashMap<>();
        for (String workType : workTypeList) {
            int count = countWorkOrderDetailByOrderIdAndWorkType(orderId, workType);
            workTypeCountMap.put(workType, count);
        }
        statistics.put("workTypeCountMap", workTypeCountMap);
        
        return statistics;
    }

    /**
     * 根据工单ID获取工单详情展示数据（用于前端弹框）
     *
     * @param orderId 工单ID
     * @return 工单详情展示数据
     */
    @Override
    public Map<String, Object> getWorkOrderDetailDisplayData(Long orderId) {
        Map<String, Object> displayData = new HashMap<>();
        
        // 获取工单基本信息
        WorkOrder workOrder = workOrderMapper.selectWorkOrderById(orderId);
        if (workOrder == null) {
            throw new ServiceException("工单不存在：" + orderId);
        }
        displayData.put("workOrder", workOrder);
        
        // 获取按维护类型分组的明细数据
        Map<String, List<WorkOrderDetail>> groupedDetails = selectWorkOrderDetailListByOrderIdGrouped(orderId);
        displayData.put("groupedDetails", groupedDetails);
        
        // 获取统计信息
        Map<String, Object> statistics = getWorkOrderDetailStatistics(orderId);
        displayData.put("statistics", statistics);
        
        return displayData;
    }
}
