package com.tunnel.service;

import com.tunnel.domain.Plan;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 服务区/收费站维护计划Service接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface PlanService {
    /**
     * 查询服务区/收费站维护计划
     *
     * @param id 服务区/收费站维护计划主键
     * @return 服务区/收费站维护计划
     */
    public Plan selectPlanById(Long id);

    /**
     * 查询服务区/收费站维护计划列表
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划集合
     */
    public List<Plan> selectPlanList(Plan plan);

    /**
     * 查询服务区/收费站维护计划列表（关联站点信息）
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划集合（包含站点信息）
     */
    public List<Plan> selectPlanListWithStation(Plan plan);

    /**
     * 新增服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    public int insertPlan(Plan plan);

    /**
     * 修改服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    public int updatePlan(Plan plan);

    /**
     * 批量删除服务区/收费站维护计划
     *
     * @param ids 需要删除的服务区/收费站维护计划主键集合
     * @return 结果
     */
    public int deletePlanByIds(Long[] ids);

    /**
     * 删除服务区/收费站维护计划信息
     *
     * @param id 服务区/收费站维护计划主键
     * @return 结果
     */
    public int deletePlanById(Long id);

    /**
     * 根据站点ID查询维护计划列表
     *
     * @param stationId 站点ID
     * @return 维护计划集合
     */
    public List<Plan> selectPlanListByStationId(String stationId);

    /**
     * 导出服务区/收费站维护计划列表
     *
     * @param response HTTP响应
     * @param plan 查询条件
     */
    public void exportPlan(HttpServletResponse response, Plan plan);

    /**
     * 导入维护计划数据
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importPlan(MultipartFile file, Boolean updateSupport) throws Exception;

    /**
     * 导入维护计划数据（使用导入VO）
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importPlanByVO(MultipartFile file, Boolean updateSupport) throws Exception;

    /**
     * 校验站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public boolean checkStationIdExists(String stationId);

    /**
     * 根据站点名称查询站点ID
     *
     * @param stationName 站点名称
     * @return 站点ID
     */
    public String selectStationIdByName(String stationName);
}
