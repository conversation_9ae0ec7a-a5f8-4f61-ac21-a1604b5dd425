package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Plan;
import com.tunnel.domain.vo.PlanImportVO;
import com.tunnel.mapper.PlanMapper;
import com.tunnel.service.PlanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 服务区/收费站维护计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class PlanServiceImpl implements PlanService {
    private static final Logger log = LoggerFactory.getLogger(PlanServiceImpl.class);

    @Autowired
    private PlanMapper planMapper;

    /**
     * 查询服务区/收费站维护计划
     *
     * @param id 服务区/收费站维护计划主键
     * @return 服务区/收费站维护计划
     */
    @Override
    public Plan selectPlanById(Long id) {
        return planMapper.selectPlanById(id);
    }

    /**
     * 查询服务区/收费站维护计划列表
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划
     */
    @Override
    public List<Plan> selectPlanList(Plan plan) {
        return planMapper.selectPlanList(plan);
    }

    /**
     * 查询服务区/收费站维护计划列表（关联站点信息）
     *
     * @param plan 服务区/收费站维护计划
     * @return 服务区/收费站维护计划集合（包含站点信息）
     */
    @Override
    public List<Plan> selectPlanListWithStation(Plan plan) {
        return planMapper.selectPlanListWithStation(plan);
    }

    /**
     * 新增服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    @Override
    public int insertPlan(Plan plan) {
        // 校验站点ID是否存在
        if (!checkStationIdExists(plan.getStationId())) {
            throw new ServiceException("站点ID不存在：" + plan.getStationId());
        }

        // 校验时间逻辑
        if (plan.getStartTime() != null && plan.getEndTime() != null) {
            if (plan.getStartTime().after(plan.getEndTime())) {
                throw new ServiceException("维护开始时间不能晚于截止时间");
            }
        }

        plan.setCreateTime(DateUtils.getNowDate());
        plan.setCreator(SecurityUtils.getUserId());
        return planMapper.insertPlan(plan);
    }

    /**
     * 修改服务区/收费站维护计划
     *
     * @param plan 服务区/收费站维护计划
     * @return 结果
     */
    @Override
    public int updatePlan(Plan plan) {
        // 校验站点ID是否存在
        if (!checkStationIdExists(plan.getStationId())) {
            throw new ServiceException("站点ID不存在：" + plan.getStationId());
        }

        // 校验时间逻辑
        if (plan.getStartTime() != null && plan.getEndTime() != null) {
            if (plan.getStartTime().after(plan.getEndTime())) {
                throw new ServiceException("维护开始时间不能晚于截止时间");
            }
        }

        plan.setModifier(SecurityUtils.getUserId());
        return planMapper.updatePlan(plan);
    }

    /**
     * 批量删除服务区/收费站维护计划
     *
     * @param ids 需要删除的服务区/收费站维护计划主键
     * @return 结果
     */
    @Override
    public int deletePlanByIds(Long[] ids) {
        return planMapper.deletePlanByIds(ids);
    }

    /**
     * 删除服务区/收费站维护计划信息
     *
     * @param id 服务区/收费站维护计划主键
     * @return 结果
     */
    @Override
    public int deletePlanById(Long id) {
        return planMapper.deletePlanById(id);
    }

    /**
     * 根据站点ID查询维护计划列表
     *
     * @param stationId 站点ID
     * @return 维护计划集合
     */
    @Override
    public List<Plan> selectPlanListByStationId(String stationId) {
        return planMapper.selectPlanListByStationId(stationId);
    }

    /**
     * 导出服务区/收费站维护计划列表
     *
     * @param response HTTP响应
     * @param plan 查询条件
     */
    @Override
    public void exportPlan(HttpServletResponse response, Plan plan) {
        List<Plan> list = planMapper.selectPlanListWithStation(plan);
        ExcelUtil<Plan> util = new ExcelUtil<Plan>(Plan.class);
        util.exportExcel(response, list, "维护计划数据");
    }

    /**
     * 校验站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    @Override
    public boolean checkStationIdExists(String stationId) {
        if (StringUtils.isEmpty(stationId)) {
            return false;
        }
        return planMapper.checkStationIdExists(stationId) > 0;
    }

    /**
     * 根据站点名称查询站点ID
     *
     * @param stationName 站点名称
     * @return 站点ID
     */
    @Override
    public String selectStationIdByName(String stationName) {
        return planMapper.selectStationIdByName(stationName);
    }

    /**
     * 导入维护计划数据
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importPlan(MultipartFile file, Boolean updateSupport) throws Exception {
        ExcelUtil<Plan> util = new ExcelUtil<Plan>(Plan.class);
        List<Plan> planList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = importPlan(planList, updateSupport, operName);
        return message;
    }

    /**
     * 导入维护计划数据
     *
     * @param planList 维护计划数据列表
     * @param isUpdateSupport 是否支持更新，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importPlan(List<Plan> planList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(planList) || planList.size() == 0) {
            throw new ServiceException("导入维护计划数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (Plan plan : planList) {
            try {
                // 处理Excel导入的特殊逻辑
                processImportData(plan);
                
                // 验证必填字段
                if (StringUtils.isEmpty(plan.getStationId())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、站点信息不能为空");
                    continue;
                }
                
                // 校验站点是否存在
                if (!checkStationIdExists(plan.getStationId())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、站点不存在：").append(plan.getStationId());
                    continue;
                }
                
                // 校验时间
                if (plan.getStartTime() == null || plan.getEndTime() == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、维护时间不能为空");
                    continue;
                }
                
                if (plan.getStartTime().after(plan.getEndTime())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、维护开始时间不能晚于截止时间");
                    continue;
                }
                
                plan.setCreator(SecurityUtils.getUserId());
                this.insertPlan(plan);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、维护计划 ").append(plan.getStationId()).append(" 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、维护计划 " + plan.getStationId() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 处理导入数据的特殊逻辑
     *
     * @param plan 维护计划数据
     */
    private void processImportData(Plan plan) throws ParseException {
        // 如果通过站点名称导入，需要转换为站点ID
        if (StringUtils.isEmpty(plan.getStationId()) && StringUtils.isNotEmpty(plan.getStationName())) {
            String stationId = selectStationIdByName(plan.getStationName());
            if (StringUtils.isNotEmpty(stationId)) {
                plan.setStationId(stationId);
            }
        }
        
        // 处理维护时间格式（支持"2025年8-12月"这种格式）
        if (StringUtils.isNotEmpty(plan.getRemark()) && plan.getRemark().contains("年") && plan.getRemark().contains("月")) {
            parseMaintenanceTime(plan, plan.getRemark());
        }
    }

    /**
     * 解析维护时间（支持"2025年8-12月"格式）
     *
     * @param plan 维护计划
     * @param timeStr 时间字符串
     */
    private void parseMaintenanceTime(Plan plan, String timeStr) throws ParseException {
        try {
            // 解析"2025年8-12月"格式
            if (timeStr.matches("\\d{4}年\\d{1,2}-\\d{1,2}月")) {
                String year = timeStr.substring(0, 4);
                String monthRange = timeStr.substring(timeStr.indexOf("年") + 1, timeStr.indexOf("月"));
                String[] months = monthRange.split("-");
                
                if (months.length == 2) {
                    int startMonth = Integer.parseInt(months[0]);
                    int endMonth = Integer.parseInt(months[1]);
                    
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date startTime = sdf.parse(year + "-" + String.format("%02d", startMonth) + "-01 00:00:00");
                    
                    // 计算结束月份的最后一天
                    int lastDay = getLastDayOfMonth(Integer.parseInt(year), endMonth);
                    Date endTime = sdf.parse(year + "-" + String.format("%02d", endMonth) + "-" + String.format("%02d", lastDay) + " 23:59:59");
                    
                    plan.setStartTime(startTime);
                    plan.setEndTime(endTime);
                }
            }
        } catch (Exception e) {
            log.warn("解析维护时间失败：" + timeStr, e);
        }
    }

    /**
     * 获取指定年月的最后一天
     */
    private int getLastDayOfMonth(int year, int month) {
        int[] daysInMonth = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        if (month == 2 && isLeapYear(year)) {
            return 29;
        }
        return daysInMonth[month - 1];
    }

    /**
     * 判断是否为闰年
     */
    private boolean isLeapYear(int year) {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
    }

    /**
     * 导入维护计划数据（使用导入VO）
     *
     * @param file Excel文件
     * @param updateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    @Override
    public String importPlanByVO(MultipartFile file, Boolean updateSupport) throws Exception {
        ExcelUtil<PlanImportVO> util = new ExcelUtil<PlanImportVO>(PlanImportVO.class);
        List<PlanImportVO> importList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = importPlanByVO(importList, updateSupport, operName);
        return message;
    }

    /**
     * 导入维护计划数据（使用导入VO）
     *
     * @param importList 导入数据列表
     * @param isUpdateSupport 是否支持更新，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importPlanByVO(List<PlanImportVO> importList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(importList) || importList.size() == 0) {
            throw new ServiceException("导入维护计划数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (PlanImportVO importVO : importList) {
            try {
                // 转换为Plan对象
                Plan plan = convertImportVOToPlan(importVO);

                if (plan == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、数据转换失败：").append(importVO.getStationName());
                    continue;
                }

                plan.setCreator(SecurityUtils.getUserId());
                this.insertPlan(plan);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、维护计划 ").append(plan.getStationId()).append(" 导入成功");
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、维护计划 " + importVO.getStationName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 将导入VO转换为Plan对象
     *
     * @param importVO 导入VO
     * @return Plan对象
     */
    private Plan convertImportVOToPlan(PlanImportVO importVO) throws ParseException {
        if (StringUtils.isEmpty(importVO.getStationName()) || StringUtils.isEmpty(importVO.getMaintenanceTime())) {
            return null;
        }

        Plan plan = new Plan();

        // 根据站点名称查找站点ID
        String stationId = selectStationIdByName(importVO.getStationName());
        if (StringUtils.isEmpty(stationId)) {
            throw new ServiceException("站点不存在：" + importVO.getStationName());
        }
        plan.setStationId(stationId);

        // 解析维护时间
        parseMaintenanceTimeFromImport(plan, importVO.getMaintenanceTime());

        // 设置备注
        plan.setRemark(importVO.getRemark());

        return plan;
    }

    /**
     * 从导入数据解析维护时间
     *
     * @param plan 维护计划
     * @param timeStr 时间字符串
     */
    private void parseMaintenanceTimeFromImport(Plan plan, String timeStr) throws ParseException {
        try {
            // 解析"2025年8-12月"格式
            if (timeStr.matches("\\d{4}年\\d{1,2}-\\d{1,2}月")) {
                String year = timeStr.substring(0, 4);
                String monthRange = timeStr.substring(timeStr.indexOf("年") + 1, timeStr.indexOf("月"));
                String[] months = monthRange.split("-");

                if (months.length == 2) {
                    int startMonth = Integer.parseInt(months[0]);
                    int endMonth = Integer.parseInt(months[1]);

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date startTime = sdf.parse(year + "-" + String.format("%02d", startMonth) + "-01 00:00:00");

                    // 计算结束月份的最后一天
                    int lastDay = getLastDayOfMonth(Integer.parseInt(year), endMonth);
                    Date endTime = sdf.parse(year + "-" + String.format("%02d", endMonth) + "-" + String.format("%02d", lastDay) + " 23:59:59");

                    plan.setStartTime(startTime);
                    plan.setEndTime(endTime);
                } else {
                    throw new ParseException("维护时间格式错误：" + timeStr, 0);
                }
            } else {
                throw new ParseException("维护时间格式错误，应为'YYYY年M-M月'格式：" + timeStr, 0);
            }
        } catch (Exception e) {
            log.error("解析维护时间失败：" + timeStr, e);
            throw new ParseException("解析维护时间失败：" + timeStr + "，错误：" + e.getMessage(), 0);
        }
    }
}
