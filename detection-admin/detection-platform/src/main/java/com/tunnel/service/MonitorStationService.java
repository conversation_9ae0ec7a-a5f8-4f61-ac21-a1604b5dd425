package com.tunnel.service;

import com.github.pagehelper.PageInfo;
import com.tunnel.domain.MonitorDTO;
import com.tunnel.domain.MonitorStation;

import java.util.List;
import java.util.Map;

/**
 * 监测站点Service接口
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface MonitorStationService {
    /**
     * 查询监测站点
     *
     * @param id 监测站点主键
     * @return 监测站点
     */
    public MonitorStation selectMonitorStationById(Long id);

    /**
     * 查询监测站点列表
     *
     * @param monitorStation 监测站点
     * @return 监测站点集合
     */
    public List<MonitorStation> selectMonitorStationList(MonitorStation monitorStation);

    /**
     * 新增监测站点
     *
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int insertMonitorStation(MonitorStation monitorStation);

    /**
     * 修改监测站点
     *
     * @param monitorStation 监测站点
     * @return 结果
     */
    public int updateMonitorStation(MonitorStation monitorStation);

    /**
     * 批量删除监测站点
     *
     * @param ids 需要删除的监测站点主键集合
     * @return 结果
     */
    public int deleteMonitorStationByIds(Long[] ids);

    /**
     * 删除监测站点信息
     *
     * @param id 监测站点主键
     * @return 结果
     */
    public int deleteMonitorStationById(Long id);

    List<MonitorStation> selectMonitorStationByType(MonitorStation monitorStation);

    Map<String, Object> selectMonitorListForChart(MonitorDTO dto);

    MonitorDTO selectTimeRange(MonitorDTO dto);

    PageInfo<Map<String, Object>> selectMonitorListByPage(MonitorDTO dto);

    /**
     * 根据监测类型获取对应的字段信息
     *
     * @param type       监测类型
     * @param systemCode 系统编码
     * @return 字段信息列表
     */
    List<Map<String, Object>> getFieldsByType(Integer type, String systemCode);

    /**
     * 查询监测站点列表并统计关联的设备数量
     *
     * @param monitorStation 监测站点查询条件
     * @return 监测站点集合（包含设备统计）
     */
    List<MonitorStation> selectMonitorStationListWithDeviceCount(MonitorStation monitorStation);
}
