package com.tunnel.service.impl;

import com.tunnel.common.exception.ServiceException;
import com.tunnel.common.utils.DateUtils;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.Plan;
import com.tunnel.domain.WorkOrder;
import com.tunnel.mapper.PlanMapper;
import com.tunnel.mapper.WorkOrderMapper;
import com.tunnel.service.WorkOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 维护工单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Service
public class WorkOrderServiceImpl implements WorkOrderService {
    private static final Logger log = LoggerFactory.getLogger(WorkOrderServiceImpl.class);

    @Autowired
    private WorkOrderMapper workOrderMapper;

    @Autowired
    private PlanMapper planMapper;

    /**
     * 查询维护工单
     *
     * @param id 维护工单主键
     * @return 维护工单
     */
    @Override
    public WorkOrder selectWorkOrderById(Long id) {
        return workOrderMapper.selectWorkOrderById(id);
    }

    /**
     * 查询维护工单列表
     *
     * @param workOrder 维护工单
     * @return 维护工单
     */
    @Override
    public List<WorkOrder> selectWorkOrderList(WorkOrder workOrder) {
        return workOrderMapper.selectWorkOrderList(workOrder);
    }

    /**
     * 查询维护工单列表（关联站点和计划信息）
     *
     * @param workOrder 维护工单
     * @return 维护工单集合（包含站点和计划信息）
     */
    @Override
    public List<WorkOrder> selectWorkOrderListWithDetails(WorkOrder workOrder) {
        return workOrderMapper.selectWorkOrderListWithDetails(workOrder);
    }

    /**
     * 新增维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWorkOrder(WorkOrder workOrder) {
        // 校验计划ID是否存在
        if (!checkPlanIdExists(workOrder.getPlanId())) {
            throw new ServiceException("计划ID不存在：" + workOrder.getPlanId());
        }

        // 校验站点ID是否存在
        if (!checkStationIdExists(workOrder.getStationId())) {
            throw new ServiceException("站点ID不存在：" + workOrder.getStationId());
        }

        // 设置默认状态为进行中
        if (workOrder.getStatus() == null) {
            workOrder.setStatus(0);
        }

        // 设置默认类型为普通工单
        if (workOrder.getType() == null) {
            workOrder.setType(0);
        }

        workOrder.setCreateTime(DateUtils.getNowDate());
        workOrder.setCreator(SecurityUtils.getUserId());
        return workOrderMapper.insertWorkOrder(workOrder);
    }

    /**
     * 修改维护工单
     *
     * @param workOrder 维护工单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWorkOrder(WorkOrder workOrder) {
        // 校验计划ID是否存在
        if (workOrder.getPlanId() != null && !checkPlanIdExists(workOrder.getPlanId())) {
            throw new ServiceException("计划ID不存在：" + workOrder.getPlanId());
        }

        // 校验站点ID是否存在
        if (workOrder.getStationId() != null && !checkStationIdExists(workOrder.getStationId())) {
            throw new ServiceException("站点ID不存在：" + workOrder.getStationId());
        }

        workOrder.setModifier(SecurityUtils.getUserId());
        return workOrderMapper.updateWorkOrder(workOrder);
    }

    /**
     * 批量删除维护工单
     *
     * @param ids 需要删除的维护工单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderByIds(Long[] ids) {
        return workOrderMapper.deleteWorkOrderByIds(ids);
    }

    /**
     * 删除维护工单信息
     *
     * @param id 维护工单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkOrderById(Long id) {
        return workOrderMapper.deleteWorkOrderById(id);
    }

    /**
     * 根据计划ID查询工单列表
     *
     * @param planId 计划ID
     * @return 工单集合
     */
    @Override
    public List<WorkOrder> selectWorkOrderListByPlanId(Long planId) {
        return workOrderMapper.selectWorkOrderListByPlanId(planId);
    }

    /**
     * 根据站点ID查询工单列表
     *
     * @param stationId 站点ID
     * @return 工单集合
     */
    @Override
    public List<WorkOrder> selectWorkOrderListByStationId(Long stationId) {
        return workOrderMapper.selectWorkOrderListByStationId(stationId);
    }

    /**
     * 导出维护工单列表
     *
     * @param response HTTP响应
     * @param workOrder 查询条件
     */
    @Override
    public void exportWorkOrder(HttpServletResponse response, WorkOrder workOrder) {
        List<WorkOrder> list = workOrderMapper.selectWorkOrderListWithDetails(workOrder);
        ExcelUtil<WorkOrder> util = new ExcelUtil<WorkOrder>(WorkOrder.class);
        util.exportExcel(response, list, "维护工单数据");
    }

    /**
     * 校验计划ID是否存在
     *
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    public boolean checkPlanIdExists(Long planId) {
        if (planId == null) {
            return false;
        }
        return workOrderMapper.checkPlanIdExists(planId) > 0;
    }

    /**
     * 校验站点ID是否存在
     *
     * @param stationId 站点ID
     * @return 结果
     */
    @Override
    public boolean checkStationIdExists(Long stationId) {
        if (stationId == null) {
            return false;
        }
        return workOrderMapper.checkStationIdExists(stationId) > 0;
    }

    /**
     * 根据计划ID和状态查询工单数量
     *
     * @param planId 计划ID
     * @param status 工单状态
     * @return 工单数量
     */
    @Override
    public int countWorkOrderByPlanIdAndStatus(Long planId, Integer status) {
        return workOrderMapper.countWorkOrderByPlanIdAndStatus(planId, status);
    }

    /**
     * 批量更新工单状态
     *
     * @param ids 工单ID数组
     * @param status 新状态
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateWorkOrderStatus(Long[] ids, Integer status) {
        return workOrderMapper.batchUpdateWorkOrderStatus(ids, status, SecurityUtils.getUserId());
    }

    /**
     * 完成工单
     *
     * @param id 工单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int completeWorkOrder(Long id) {
        WorkOrder workOrder = new WorkOrder();
        workOrder.setId(id);
        workOrder.setStatus(1); // 已完成
        workOrder.setModifier(SecurityUtils.getUserId());
        return workOrderMapper.updateWorkOrder(workOrder);
    }

    /**
     * 重新开始工单
     *
     * @param id 工单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int restartWorkOrder(Long id) {
        WorkOrder workOrder = new WorkOrder();
        workOrder.setId(id);
        workOrder.setStatus(0); // 进行中
        workOrder.setModifier(SecurityUtils.getUserId());
        return workOrderMapper.updateWorkOrder(workOrder);
    }

    /**
     * 根据计划创建工单
     *
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int createWorkOrderByPlan(Long planId) {
        // 校验计划是否存在
        if (!checkPlanIdExists(planId)) {
            throw new ServiceException("计划不存在：" + planId);
        }

        // 获取计划信息
        Plan plan = planMapper.selectPlanById(planId);
        if (plan == null) {
            throw new ServiceException("计划不存在：" + planId);
        }

        // 检查是否已经存在工单
        int existingCount = countWorkOrderByPlanIdAndStatus(planId, null);
        if (existingCount > 0) {
            throw new ServiceException("该计划已存在工单，不能重复创建");
        }

        // 根据站点编码查找站点ID
        Long stationId = findStationIdByCode(plan.getStationId());
        if (stationId == null) {
            throw new ServiceException("站点不存在：" + plan.getStationId());
        }

        // 创建工单
        WorkOrder workOrder = new WorkOrder();
        workOrder.setPlanId(planId);
        workOrder.setStationId(stationId);
        workOrder.setStatus(0); // 进行中
        workOrder.setType(0); // 普通工单
        workOrder.setRemark("根据计划自动创建的工单");
        workOrder.setCreator(SecurityUtils.getUserId());
        workOrder.setCreateTime(DateUtils.getNowDate());

        return workOrderMapper.insertWorkOrder(workOrder);
    }

    /**
     * 根据站点编码查找站点ID
     *
     * @param stationCode 站点编码
     * @return 站点ID
     */
    private Long findStationIdByCode(String stationCode) {
        return workOrderMapper.selectStationIdByCode(stationCode);
    }
}
