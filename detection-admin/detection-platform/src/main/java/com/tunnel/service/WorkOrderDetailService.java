package com.tunnel.service;

import com.tunnel.domain.WorkOrderDetail;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 维护工单明细Service接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface WorkOrderDetailService {
    /**
     * 查询维护工单明细
     *
     * @param id 维护工单明细主键
     * @return 维护工单明细
     */
    public WorkOrderDetail selectWorkOrderDetailById(Long id);

    /**
     * 查询维护工单明细列表
     *
     * @param workOrderDetail 维护工单明细
     * @return 维护工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailList(WorkOrderDetail workOrderDetail);

    /**
     * 根据工单ID查询工单明细列表
     *
     * @param orderId 工单ID
     * @return 工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderId(Long orderId);

    /**
     * 根据工单ID查询工单明细列表（按维护类型分组）
     *
     * @param orderId 工单ID
     * @return 按维护类型分组的工单明细Map，key为维护类型，value为明细列表
     */
    public Map<String, List<WorkOrderDetail>> selectWorkOrderDetailListByOrderIdGrouped(Long orderId);

    /**
     * 根据工单ID数组查询工单明细列表
     *
     * @param orderIds 工单ID数组
     * @return 工单明细集合
     */
    public List<WorkOrderDetail> selectWorkOrderDetailListByOrderIds(Long[] orderIds);

    /**
     * 新增维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    public int insertWorkOrderDetail(WorkOrderDetail workOrderDetail);

    /**
     * 批量新增维护工单明细
     *
     * @param workOrderDetailList 工单明细列表
     * @return 结果
     */
    public int batchInsertWorkOrderDetail(List<WorkOrderDetail> workOrderDetailList);

    /**
     * 修改维护工单明细
     *
     * @param workOrderDetail 维护工单明细
     * @return 结果
     */
    public int updateWorkOrderDetail(WorkOrderDetail workOrderDetail);

    /**
     * 批量删除维护工单明细
     *
     * @param ids 需要删除的维护工单明细主键集合
     * @return 结果
     */
    public int deleteWorkOrderDetailByIds(Long[] ids);

    /**
     * 删除维护工单明细信息
     *
     * @param id 维护工单明细主键
     * @return 结果
     */
    public int deleteWorkOrderDetailById(Long id);

    /**
     * 根据工单ID删除工单明细
     *
     * @param orderId 工单ID
     * @return 结果
     */
    public int deleteWorkOrderDetailByOrderId(Long orderId);

    /**
     * 根据工单ID数组删除工单明细
     *
     * @param orderIds 工单ID数组
     * @return 结果
     */
    public int deleteWorkOrderDetailByOrderIds(Long[] orderIds);

    /**
     * 导出维护工单明细列表
     *
     * @param response HTTP响应
     * @param workOrderDetail 查询条件
     */
    public void exportWorkOrderDetail(HttpServletResponse response, WorkOrderDetail workOrderDetail);

    /**
     * 根据工单ID统计明细数量
     *
     * @param orderId 工单ID
     * @return 明细数量
     */
    public int countWorkOrderDetailByOrderId(Long orderId);

    /**
     * 根据工单ID和维护类型统计明细数量
     *
     * @param orderId 工单ID
     * @param workType 维护类型
     * @return 明细数量
     */
    public int countWorkOrderDetailByOrderIdAndWorkType(Long orderId, String workType);

    /**
     * 获取工单的维护类型列表（去重）
     *
     * @param orderId 工单ID
     * @return 维护类型列表
     */
    public List<String> selectWorkTypeListByOrderId(Long orderId);

    /**
     * 检查工单ID是否存在
     *
     * @param orderId 工单ID
     * @return 结果
     */
    public boolean checkOrderIdExists(Long orderId);

    /**
     * 获取工单详情统计信息
     *
     * @param orderId 工单ID
     * @return 统计信息Map
     */
    public Map<String, Object> getWorkOrderDetailStatistics(Long orderId);

    /**
     * 根据工单ID获取工单详情展示数据（用于前端弹框）
     *
     * @param orderId 工单ID
     * @return 工单详情展示数据
     */
    public Map<String, Object> getWorkOrderDetailDisplayData(Long orderId);
}
