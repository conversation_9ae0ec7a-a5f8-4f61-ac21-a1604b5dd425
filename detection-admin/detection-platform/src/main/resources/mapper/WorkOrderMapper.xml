<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.WorkOrderMapper">
    
    <resultMap type="com.tunnel.domain.WorkOrder" id="WorkOrderResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <resultMap type="com.tunnel.domain.WorkOrder" id="WorkOrderWithDetailsResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="status"    column="status"    />
        <result property="type"    column="type"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <!-- 关联站点信息 -->
        <result property="stationCode"    column="station_code"    />
        <result property="stationName"    column="station_name"    />
        <result property="district"    column="district"    />
        <result property="companyName"    column="company_name"    />
        <result property="openingStatus"    column="opening_status"    />
        <result property="maintenanceCategory"    column="maintenance_category"    />
        <result property="sewageProcess"    column="sewage_process"    />
        <result property="equipmentModel"    column="equipment_model"    />
        <result property="equipmentQuantity"    column="equipment_quantity"    />
        <result property="processingScale"    column="processing_scale"    />
        <result property="maintenanceType"    column="maintenance_type"    />
        <result property="pollutionType"    column="pollution_type"    />
        <result property="stationState"    column="station_state"    />
        <result property="faulty"    column="faulty"    />
        <!-- 关联计划信息 -->
        <result property="planStartTime"    column="plan_start_time"    />
        <result property="planEndTime"    column="plan_end_time"    />
        <result property="planRemark"    column="plan_remark"    />
    </resultMap>

    <sql id="tableName">sc_work_order</sql>

    <sql id="allColumn">
        id, station_id, plan_id, status, type, remark, create_time, update_time, creator, modifier
    </sql>

    <sql id="selectWorkOrderVo">
        select <include refid="allColumn"/> from <include refid="tableName"/>
    </sql>

    <sql id="selectWorkOrderWithDetailsVo">
        select w.id, w.station_id, w.plan_id, w.status, w.type, w.remark,
               w.create_time, w.update_time, w.creator, w.modifier,
               s.code as station_code, s.name as station_name, s.district, s.company_name, 
               s.opening_status, s.maintenance_category, s.sewage_process,
               s.equipment_model, s.equipment_quantity, s.processing_scale, 
               s.maintenance_type, s.pollution_type, s.state as station_state, s.faulty,
               p.start_time as plan_start_time, p.end_time as plan_end_time, p.remark as plan_remark
        from sc_work_order w
        left join sc_monitor_station s on w.station_id = s.id
        left join sc_plan p on w.plan_id = p.id
    </sql>

    <select id="selectWorkOrderList" parameterType="com.tunnel.domain.WorkOrder" resultMap="WorkOrderResult">
        <include refid="selectWorkOrderVo"/>
        <where>
            <if test="stationId != null"> and station_id = #{stationId}</if>
            <if test="planId != null"> and plan_id = #{planId}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="statusArray != null and statusArray.length > 0">
                and status in
                <foreach item="status" collection="statusArray" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="type != null"> and type = #{type}</if>
            <if test="typeArray != null and typeArray.length > 0">
                and type in
                <foreach item="type" collection="typeArray" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="createTimeBegin != null and createTimeBegin != ''"> and create_time &gt;= #{createTimeBegin}</if>
            <if test="createTimeEnd != null and createTimeEnd != ''"> and create_time &lt;= #{createTimeEnd}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectWorkOrderListWithDetails" parameterType="com.tunnel.domain.WorkOrder" resultMap="WorkOrderWithDetailsResult">
        <include refid="selectWorkOrderWithDetailsVo"/>
        <where>
            <if test="stationId != null"> and w.station_id = #{stationId}</if>
            <if test="planId != null"> and w.plan_id = #{planId}</if>
            <if test="status != null"> and w.status = #{status}</if>
            <if test="statusArray != null and statusArray.length > 0">
                and w.status in
                <foreach item="status" collection="statusArray" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="type != null"> and w.type = #{type}</if>
            <if test="typeArray != null and typeArray.length > 0">
                and w.type in
                <foreach item="type" collection="typeArray" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="stationNameOrCode != null and stationNameOrCode != ''">
                and (s.name like concat('%', #{stationNameOrCode}, '%') or s.code like concat('%', #{stationNameOrCode}, '%'))
            </if>
            <if test="district != null and district != ''"> and s.district like concat('%', #{district}, '%')</if>
            <if test="companyName != null and companyName != ''"> and s.company_name like concat('%', #{companyName}, '%')</if>
            <if test="createTimeBegin != null and createTimeBegin != ''"> and w.create_time &gt;= #{createTimeBegin}</if>
            <if test="createTimeEnd != null and createTimeEnd != ''"> and w.create_time &lt;= #{createTimeEnd}</if>
            <if test="planStartTimeBegin != null and planStartTimeBegin != ''"> and p.start_time &gt;= #{planStartTimeBegin}</if>
            <if test="planStartTimeEnd != null and planStartTimeEnd != ''"> and p.start_time &lt;= #{planStartTimeEnd}</if>
            <if test="remark != null  and remark != ''"> and w.remark like concat('%', #{remark}, '%')</if>
        </where>
        order by w.create_time desc
    </select>
    
    <select id="selectWorkOrderById" parameterType="Long" resultMap="WorkOrderWithDetailsResult">
        <include refid="selectWorkOrderWithDetailsVo"/>
        where w.id = #{id}
    </select>

    <select id="selectWorkOrderListByPlanId" parameterType="Long" resultMap="WorkOrderResult">
        <include refid="selectWorkOrderVo"/>
        where plan_id = #{planId}
        order by create_time desc
    </select>

    <select id="selectWorkOrderListByStationId" parameterType="Long" resultMap="WorkOrderResult">
        <include refid="selectWorkOrderVo"/>
        where station_id = #{stationId}
        order by create_time desc
    </select>

    <select id="checkPlanIdExists" parameterType="Long" resultType="int">
        select count(1) from sc_plan where id = #{planId}
    </select>

    <select id="checkStationIdExists" parameterType="Long" resultType="int">
        select count(1) from sc_monitor_station where id = #{stationId}
    </select>

    <select id="countWorkOrder" parameterType="com.tunnel.domain.WorkOrder" resultType="int">
        select count(1) from <include refid="tableName"/>
        <where>  
            <if test="stationId != null"> and station_id = #{stationId}</if>
            <if test="planId != null"> and plan_id = #{planId}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="countWorkOrderByPlanIdAndStatus" resultType="int">
        select count(1) from <include refid="tableName"/>
        where plan_id = #{planId}
        <if test="status != null"> and status = #{status}</if>
    </select>

    <select id="selectStationIdByCode" parameterType="String" resultType="Long">
        select id from sc_monitor_station where code = #{stationCode} limit 1
    </select>

    <insert id="insertWorkOrder" parameterType="com.tunnel.domain.WorkOrder" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationId != null">station_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="status != null">status,</if>
            <if test="type != null">type,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            create_time, update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationId != null">#{stationId},</if>
            <if test="planId != null">#{planId},</if>
            <if test="status != null">#{status},</if>
            <if test="type != null">#{type},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            now(), now()
         </trim>
    </insert>

    <update id="updateWorkOrder" parameterType="com.tunnel.domain.WorkOrder">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="planId != null">plan_id = #{planId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <update id="batchUpdateWorkOrderStatus">
        update <include refid="tableName"/>
        set status = #{status}, modifier = #{modifier}, update_time = now()
        where id in 
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteWorkOrderById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteWorkOrderByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
