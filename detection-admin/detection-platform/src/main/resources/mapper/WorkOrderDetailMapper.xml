<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.WorkOrderDetailMapper">
    
    <resultMap type="com.tunnel.domain.WorkOrderDetail" id="WorkOrderDetailResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="workType"    column="work_type"    />
        <result property="workTitle"    column="work_title"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="picUrl"    column="pic_url"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <resultMap type="com.tunnel.domain.WorkOrderDetail" id="WorkOrderDetailWithWorkOrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="workType"    column="work_type"    />
        <result property="workTitle"    column="work_title"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="picUrl"    column="pic_url"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <!-- 关联工单信息 -->
        <result property="workOrderId"    column="work_order_id"    />
        <result property="workOrderStatus"    column="work_order_status"    />
        <result property="workOrderType"    column="work_order_type"    />
        <result property="stationCode"    column="station_code"    />
        <result property="stationName"    column="station_name"    />
    </resultMap>

    <sql id="tableName">sc_work_order_detail</sql>

    <sql id="allColumn">
        id, order_id, work_type, work_title, longitude, latitude, pic_url, remark, create_time, update_time, creator, modifier
    </sql>

    <sql id="selectWorkOrderDetailVo">
        select <include refid="allColumn"/> from <include refid="tableName"/>
    </sql>

    <sql id="selectWorkOrderDetailWithWorkOrderVo">
        select d.id, d.order_id, d.work_type, d.work_title, d.longitude, d.latitude, d.pic_url, d.remark,
               d.create_time, d.update_time, d.creator, d.modifier,
               w.id as work_order_id, w.status as work_order_status, w.type as work_order_type,
               s.code as station_code, s.name as station_name
        from sc_work_order_detail d
        left join sc_work_order w on d.order_id = w.id
        left join sc_monitor_station s on w.station_id = s.id
    </sql>

    <select id="selectWorkOrderDetailList" parameterType="com.tunnel.domain.WorkOrderDetail" resultMap="WorkOrderDetailResult">
        <include refid="selectWorkOrderDetailVo"/>
        <where>  
            <if test="orderId != null"> and order_id = #{orderId}</if>
            <if test="orderIdArray != null and orderIdArray.length > 0">
                and order_id in
                <foreach item="orderId" collection="orderIdArray" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
            <if test="workType != null and workType != ''"> and work_type = #{workType}</if>
            <if test="workTypeArray != null and workTypeArray.length > 0">
                and work_type in
                <foreach item="workType" collection="workTypeArray" open="(" separator="," close=")">
                    #{workType}
                </foreach>
            </if>
            <if test="workTitle != null"> and work_title = #{workTitle}</if>
            <if test="workTitleArray != null and workTitleArray.length > 0">
                and work_title in
                <foreach item="workTitle" collection="workTitleArray" open="(" separator="," close=")">
                    #{workTitle}
                </foreach>
            </if>
            <if test="createTimeBegin != null and createTimeBegin != ''"> and create_time &gt;= #{createTimeBegin}</if>
            <if test="createTimeEnd != null and createTimeEnd != ''"> and create_time &lt;= #{createTimeEnd}</if>
            <if test="remark != null and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by work_type, create_time desc
    </select>

    <select id="selectWorkOrderDetailById" parameterType="Long" resultMap="WorkOrderDetailWithWorkOrderResult">
        <include refid="selectWorkOrderDetailWithWorkOrderVo"/>
        where d.id = #{id}
    </select>

    <select id="selectWorkOrderDetailListByOrderId" parameterType="Long" resultMap="WorkOrderDetailResult">
        <include refid="selectWorkOrderDetailVo"/>
        where order_id = #{orderId}
        order by work_type, create_time desc
    </select>

    <select id="selectWorkOrderDetailListByOrderIdGrouped" parameterType="Long" resultMap="WorkOrderDetailResult">
        <include refid="selectWorkOrderDetailVo"/>
        where order_id = #{orderId}
        order by work_type, work_title, create_time desc
    </select>

    <select id="selectWorkOrderDetailListByOrderIds" resultMap="WorkOrderDetailResult">
        <include refid="selectWorkOrderDetailVo"/>
        where order_id in
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        order by order_id, work_type, create_time desc
    </select>

    <select id="countWorkOrderDetail" parameterType="com.tunnel.domain.WorkOrderDetail" resultType="int">
        select count(1) from <include refid="tableName"/>
        <where>  
            <if test="orderId != null"> and order_id = #{orderId}</if>
            <if test="workType != null and workType != ''"> and work_type = #{workType}</if>
            <if test="workTitle != null"> and work_title = #{workTitle}</if>
            <if test="remark != null and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="countWorkOrderDetailByOrderId" parameterType="Long" resultType="int">
        select count(1) from <include refid="tableName"/>
        where order_id = #{orderId}
    </select>

    <select id="countWorkOrderDetailByOrderIdAndWorkType" resultType="int">
        select count(1) from <include refid="tableName"/>
        where order_id = #{orderId} and work_type = #{workType}
    </select>

    <select id="selectWorkTypeListByOrderId" parameterType="Long" resultType="String">
        select distinct work_type from <include refid="tableName"/>
        where order_id = #{orderId}
        order by work_type
    </select>

    <select id="checkOrderIdExists" parameterType="Long" resultType="int">
        select count(1) from sc_work_order where id = #{orderId}
    </select>

    <insert id="insertWorkOrderDetail" parameterType="com.tunnel.domain.WorkOrderDetail" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="workType != null and workType != ''">work_type,</if>
            <if test="workTitle != null">work_title,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="picUrl != null">pic_url,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            create_time, update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="workType != null and workType != ''">#{workType},</if>
            <if test="workTitle != null">#{workTitle},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="picUrl != null">#{picUrl},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            now(), now()
         </trim>
    </insert>

    <insert id="batchInsertWorkOrderDetail" parameterType="java.util.List">
        insert into <include refid="tableName"/>
        (order_id, work_type, work_title, longitude, latitude, pic_url, remark, creator, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.workType}, #{item.workTitle}, #{item.longitude}, #{item.latitude}, 
             #{item.picUrl}, #{item.remark}, #{item.creator}, now(), now())
        </foreach>
    </insert>

    <update id="updateWorkOrderDetail" parameterType="com.tunnel.domain.WorkOrderDetail">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="workType != null and workType != ''">work_type = #{workType},</if>
            <if test="workTitle != null">work_title = #{workTitle},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="picUrl != null">pic_url = #{picUrl},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkOrderDetailById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deleteWorkOrderDetailByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWorkOrderDetailByOrderId" parameterType="Long">
        delete from <include refid="tableName"/> where order_id = #{orderId}
    </delete>

    <delete id="deleteWorkOrderDetailByOrderIds">
        delete from <include refid="tableName"/> where order_id in 
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>
