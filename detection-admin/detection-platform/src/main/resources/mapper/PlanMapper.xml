<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tunnel.mapper.PlanMapper">
    
    <resultMap type="com.tunnel.domain.Plan" id="PlanResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
    </resultMap>

    <resultMap type="com.tunnel.domain.Plan" id="PlanWithStationResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="creator"    column="creator"    />
        <result property="modifier"    column="modifier"    />
        <!-- 关联站点信息 -->
        <result property="stationCode"    column="station_code"    />
        <result property="stationName"    column="station_name"    />
        <result property="district"    column="district"    />
        <result property="companyName"    column="company_name"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="openingStatus"    column="opening_status"    />
        <result property="maintenanceCategory"    column="maintenance_category"    />
        <result property="sewageProcess"    column="sewage_process"    />
        <result property="equipmentModel"    column="equipment_model"    />
        <result property="equipmentQuantity"    column="equipment_quantity"    />
        <result property="processingScale"    column="processing_scale"    />
        <result property="maintenanceType"    column="maintenance_type"    />
        <result property="pollutionType"    column="pollution_type"    />
        <result property="state"    column="state"    />
        <result property="faulty"    column="faulty"    />
    </resultMap>

    <sql id="tableName">sc_plan</sql>

    <sql id="allColumn">
        id, station_id, start_time, end_time, remark, create_time, update_time, creator, modifier
    </sql>

    <sql id="selectPlanVo">
        select <include refid="allColumn"/> from <include refid="tableName"/>
    </sql>

    <sql id="selectPlanWithStationVo">
        select p.id, p.station_id, p.start_time, p.end_time, p.remark,
               p.create_time, p.update_time, p.creator, p.modifier,
               s.code as station_code, s.name as station_name, s.district, s.company_name,
               s.lat, s.lon, s.opening_status, s.maintenance_category, s.sewage_process,
               s.equipment_model, s.equipment_quantity, s.processing_scale,
               s.maintenance_type, s.pollution_type, s.state, s.faulty
        from sc_plan p
        left join sc_monitor_station s on p.station_id = s.code
    </sql>

    <select id="selectPlanList" parameterType="com.tunnel.domain.Plan" resultMap="PlanResult">
        <include refid="selectPlanVo"/>
        <where>  
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="startTimeBegin != null and startTimeBegin != ''"> and start_time &gt;= #{startTimeBegin}</if>
            <if test="startTimeEnd != null and startTimeEnd != ''"> and start_time &lt;= #{startTimeEnd}</if>
            <if test="endTimeBegin != null and endTimeBegin != ''"> and end_time &gt;= #{endTimeBegin}</if>
            <if test="endTimeEnd != null and endTimeEnd != ''"> and end_time &lt;= #{endTimeEnd}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectPlanListWithStation" parameterType="com.tunnel.domain.Plan" resultMap="PlanWithStationResult">
        <include refid="selectPlanWithStationVo"/>
        <where>
            <if test="stationId != null  and stationId != ''"> and p.station_id = #{stationId}</if>
            <if test="stationNameOrCode != null and stationNameOrCode != ''">
                and (s.name like concat('%', #{stationNameOrCode}, '%') or s.code like concat('%', #{stationNameOrCode}, '%'))
            </if>
            <if test="district != null and district != ''"> and s.district like concat('%', #{district}, '%')</if>
            <if test="companyName != null and companyName != ''"> and s.company_name like concat('%', #{companyName}, '%')</if>
            <if test="startTime != null "> and p.start_time = #{startTime}</if>
            <if test="endTime != null "> and p.end_time = #{endTime}</if>
            <if test="startTimeBegin != null and startTimeBegin != ''"> and p.start_time &gt;= #{startTimeBegin}</if>
            <if test="startTimeEnd != null and startTimeEnd != ''"> and p.start_time &lt;= #{startTimeEnd}</if>
            <if test="endTimeBegin != null and endTimeBegin != ''"> and p.end_time &gt;= #{endTimeBegin}</if>
            <if test="endTimeEnd != null and endTimeEnd != ''"> and p.end_time &lt;= #{endTimeEnd}</if>
            <if test="remark != null  and remark != ''"> and p.remark like concat('%', #{remark}, '%')</if>
            <if test="state != null"> and s.state = #{state}</if>
            <if test="faulty != null"> and s.faulty = #{faulty}</if>
        </where>
        order by p.create_time desc
    </select>
    
    <select id="selectPlanById" parameterType="Long" resultMap="PlanWithStationResult">
        <include refid="selectPlanWithStationVo"/>
        where p.id = #{id}
    </select>

    <select id="selectPlanListByStationId" parameterType="String" resultMap="PlanResult">
        <include refid="selectPlanVo"/>
        where station_id = #{stationId}
        order by start_time desc
    </select>

    <select id="selectStationIdByName" parameterType="String" resultType="String">
        select code from sc_monitor_station where name = #{stationName} limit 1
    </select>

    <select id="checkStationIdExists" parameterType="String" resultType="int">
        select count(1) from sc_monitor_station where code = #{stationId}
    </select>

    <select id="countPlan" parameterType="com.tunnel.domain.Plan" resultType="int">
        select count(1) from <include refid="tableName"/>
        <where>  
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <insert id="insertPlan" parameterType="com.tunnel.domain.Plan" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationId != null and stationId != ''">station_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            create_time, update_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="modifier != null">#{modifier},</if>
            now(), now()
         </trim>
    </insert>

    <update id="updatePlan" parameterType="com.tunnel.domain.Plan">
        update <include refid="tableName"/>
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null and stationId != ''">station_id = #{stationId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePlanById" parameterType="Long">
        delete from <include refid="tableName"/> where id = #{id}
    </delete>

    <delete id="deletePlanByIds" parameterType="String">
        delete from <include refid="tableName"/> where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
