package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.WorkOrder;
import com.tunnel.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 维护工单Controller
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/platform/workOrder")
@Api(tags = "维护工单管理")
public class WorkOrderController extends BaseController {
    @Autowired
    private WorkOrderService workOrderService;

    /**
     * 查询维护工单列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取维护工单列表", notes = "分页查询维护工单列表")
    @Operation(summary = "获取维护工单列表", description = "分页查询维护工单列表")
    public TableDataInfo list(WorkOrder workOrder) {
        startPage();
        List<WorkOrder> list = workOrderService.selectWorkOrderListWithDetails(workOrder);
        return getDataTable(list);
    }

    /**
     * 导出维护工单列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:export')")
    @Log(title = "维护工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出维护工单", notes = "导出维护工单数据到Excel")
    @Operation(summary = "导出维护工单", description = "导出维护工单数据到Excel")
    public void export(HttpServletResponse response, WorkOrder workOrder) {
        workOrderService.exportWorkOrder(response, workOrder);
    }

    /**
     * 获取维护工单详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取维护工单详细", notes = "根据ID获取维护工单详情")
    @Operation(summary = "获取维护工单详细", description = "根据ID获取维护工单详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(workOrderService.selectWorkOrderById(id));
    }

    /**
     * 新增维护工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:add')")
    @Log(title = "维护工单", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增维护工单", notes = "新增单条维护工单")
    @Operation(summary = "新增维护工单", description = "新增单条维护工单")
    public AjaxResult add(@Validated @RequestBody WorkOrder workOrder) {
        return toAjax(workOrderService.insertWorkOrder(workOrder));
    }

    /**
     * 修改维护工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:edit')")
    @Log(title = "维护工单", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改维护工单", notes = "修改维护工单")
    @Operation(summary = "修改维护工单", description = "修改维护工单")
    public AjaxResult edit(@Validated @RequestBody WorkOrder workOrder) {
        return toAjax(workOrderService.updateWorkOrder(workOrder));
    }

    /**
     * 删除维护工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:remove')")
    @Log(title = "维护工单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除维护工单", notes = "批量删除维护工单")
    @Operation(summary = "删除维护工单", description = "批量删除维护工单")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(workOrderService.deleteWorkOrderByIds(ids));
    }

    /**
     * 根据计划ID查询工单列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:list')")
    @GetMapping("/listByPlan/{planId}")
    @ApiOperation(value = "根据计划查询工单", notes = "根据计划ID查询工单列表")
    @Operation(summary = "根据计划查询工单", description = "根据计划ID查询工单列表")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult listByPlan(@PathVariable Long planId) {
        List<WorkOrder> list = workOrderService.selectWorkOrderListByPlanId(planId);
        return success(list);
    }

    /**
     * 根据站点ID查询工单列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:list')")
    @GetMapping("/listByStation/{stationId}")
    @ApiOperation(value = "根据站点查询工单", notes = "根据站点ID查询工单列表")
    @Operation(summary = "根据站点查询工单", description = "根据站点ID查询工单列表")
    @ApiImplicitParam(name = "stationId", value = "站点ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult listByStation(@PathVariable Long stationId) {
        List<WorkOrder> list = workOrderService.selectWorkOrderListByStationId(stationId);
        return success(list);
    }

    /**
     * 完成工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:edit')")
    @Log(title = "维护工单", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{id}")
    @ApiOperation(value = "完成工单", notes = "将工单状态设置为已完成")
    @Operation(summary = "完成工单", description = "将工单状态设置为已完成")
    @ApiImplicitParam(name = "id", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult complete(@PathVariable Long id) {
        return toAjax(workOrderService.completeWorkOrder(id));
    }

    /**
     * 重新开始工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:edit')")
    @Log(title = "维护工单", businessType = BusinessType.UPDATE)
    @PutMapping("/restart/{id}")
    @ApiOperation(value = "重新开始工单", notes = "将工单状态设置为进行中")
    @Operation(summary = "重新开始工单", description = "将工单状态设置为进行中")
    @ApiImplicitParam(name = "id", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult restart(@PathVariable Long id) {
        return toAjax(workOrderService.restartWorkOrder(id));
    }

    /**
     * 批量更新工单状态
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:edit')")
    @Log(title = "维护工单", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    @ApiOperation(value = "批量更新工单状态", notes = "批量更新工单状态")
    @Operation(summary = "批量更新工单状态", description = "批量更新工单状态")
    public AjaxResult batchUpdateStatus(@RequestParam Long[] ids, @RequestParam Integer status) {
        return toAjax(workOrderService.batchUpdateWorkOrderStatus(ids, status));
    }

    /**
     * 根据计划创建工单
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrder:add')")
    @Log(title = "维护工单", businessType = BusinessType.INSERT)
    @PostMapping("/createByPlan/{planId}")
    @ApiOperation(value = "根据计划创建工单", notes = "根据计划ID创建工单")
    @Operation(summary = "根据计划创建工单", description = "根据计划ID创建工单")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult createByPlan(@PathVariable Long planId) {
        return toAjax(workOrderService.createWorkOrderByPlan(planId));
    }

    /**
     * 校验计划ID是否存在
     */
    @GetMapping("/checkPlanId/{planId}")
    @ApiOperation(value = "校验计划ID", notes = "校验计划ID是否存在")
    @Operation(summary = "校验计划ID", description = "校验计划ID是否存在")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult checkPlanId(@PathVariable Long planId) {
        boolean exists = workOrderService.checkPlanIdExists(planId);
        return success(exists);
    }

    /**
     * 校验站点ID是否存在
     */
    @GetMapping("/checkStationId/{stationId}")
    @ApiOperation(value = "校验站点ID", notes = "校验站点ID是否存在")
    @Operation(summary = "校验站点ID", description = "校验站点ID是否存在")
    @ApiImplicitParam(name = "stationId", value = "站点ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult checkStationId(@PathVariable Long stationId) {
        boolean exists = workOrderService.checkStationIdExists(stationId);
        return success(exists);
    }

    /**
     * 根据计划ID和状态查询工单数量
     */
    @GetMapping("/countByPlanAndStatus")
    @ApiOperation(value = "查询工单数量", notes = "根据计划ID和状态查询工单数量")
    @Operation(summary = "查询工单数量", description = "根据计划ID和状态查询工单数量")
    public AjaxResult countByPlanAndStatus(@RequestParam Long planId, @RequestParam(required = false) Integer status) {
        int count = workOrderService.countWorkOrderByPlanIdAndStatus(planId, status);
        return success(count);
    }

    /**
     * 下载工单导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载工单导入模板")
    @Operation(summary = "下载导入模板", description = "下载工单导入模板")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WorkOrder> util = new ExcelUtil<WorkOrder>(WorkOrder.class);
        util.importTemplateExcel(response, "维护工单数据");
    }
}
