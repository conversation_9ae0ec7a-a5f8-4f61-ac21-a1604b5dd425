package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.SecurityUtils;
import com.tunnel.common.utils.StringUtils;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.MonitorStation;
import com.tunnel.mapper.MonitorStationMapper;
import com.tunnel.service.MonitorStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 监测站点Controller
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Api(tags = "监测站点管理")
@RestController
@RequestMapping("/detection/station")
@Validated
public class MonitorStationController extends BaseController {
    @Autowired
    private MonitorStationService monitorStationService;
    @Resource
    private MonitorStationMapper monitorStationMapper;

    /**
     * 查询所有监测站点列表（不分页，包含设备统计）
     */
    @ApiOperation("查询所有监测站点列表（不分页，包含设备统计）")
    @PostMapping("/listAllForPage")
    public AjaxResult listAllForPage(@RequestBody MonitorStation monitorStation) {
        List<MonitorStation> list = monitorStationMapper.selectAll(monitorStation);
        return AjaxResult.success(list);
    }

    /**
     * 查询监测站点列表（分页）
     */
    @ApiOperation("查询监测站点列表（分页）")
    @PostMapping("/selectByPage")
    public TableDataInfo selectByPage(@RequestBody MonitorStation monitorStation) {
        startPage();
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(monitorStation);
        return getDataTable(list);
    }

    /**
     * 查询所有监测站点列表（不分页，包含设备统计）
     */
    @ApiOperation("查询所有监测站点列表（不分页，包含设备统计）")
    @PostMapping("/listAll")
    public AjaxResult listAll(@RequestBody MonitorStation monitorStation) {
        // 清除分页参数，确保不分页
        monitorStation.setPageNum(null);
        monitorStation.setPageSize(null);
        List<MonitorStation> list = monitorStationService.selectMonitorStationListWithDeviceCount(monitorStation);
        return AjaxResult.success(list);
    }

    /**
     * 查询监测站点列表（不分页）
     */
    @ApiOperation("查询监测站点列表（不分页）")
    @PostMapping("/list")
    public AjaxResult list(@RequestBody MonitorStation monitorStation) {
        // 清除分页参数，确保不分页
        monitorStation.setPageNum(null);
        monitorStation.setPageSize(null);
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(monitorStation);
        return AjaxResult.success(list);
    }

    /**
     * 根据站点编码查询监测站点
     */
    @ApiOperation("根据站点编码查询监测站点")
    @GetMapping("/getByCode/{code}")
    public AjaxResult getByCode(@PathVariable("code") String code) {
        if (StringUtils.isEmpty(code)) {
            return AjaxResult.error("站点编码不能为空");
        }
        MonitorStation query = new MonitorStation();
        query.setCode(code);
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(query);
        if (list != null && !list.isEmpty()) {
            return AjaxResult.success(list.get(0));
        }
        return AjaxResult.error("未找到对应的监测站点");
    }

    /**
     * 导出监测站点列表
     */
    @ApiOperation("导出监测站点列表")
    @PreAuthorize("@ss.hasPermi('domain:station:export')")
    @Log(title = "监测站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody MonitorStation monitorStation) {
        List<MonitorStation> list = monitorStationService.selectMonitorStationList(monitorStation);
        ExcelUtil<MonitorStation> util = new ExcelUtil<MonitorStation>(MonitorStation.class);
        util.exportExcel(response, list, "监测站点数据");
    }

    /**
     * 获取监测站点详细信息
     */
    @ApiOperation("获取监测站点详细信息")
    @PreAuthorize("@ss.hasPermi('domain:station:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") @NotNull(message = "站点ID不能为空") Long id) {
        MonitorStation station = monitorStationService.selectMonitorStationById(id);
        if (station == null) {
            return AjaxResult.error("未找到对应的监测站点");
        }
        return AjaxResult.success(station);
    }

    /**
     * 新增监测站点
     */
    @ApiOperation("新增监测站点")
    @PreAuthorize("@ss.hasPermi('domain:station:add')")
    @Log(title = "监测站点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Valid MonitorStation monitorStation) {
        // 验证站点编码是否已存在
        if (StringUtils.isNotEmpty(monitorStation.getCode())) {
            MonitorStation existStation = new MonitorStation();
            existStation.setCode(monitorStation.getCode());
            List<MonitorStation> existList = monitorStationService.selectMonitorStationList(existStation);
            if (existList != null && !existList.isEmpty()) {
                return AjaxResult.error("站点编码已存在");
            }
        }

        // 设置创建人
        monitorStation.setCreator(SecurityUtils.getUserId());

        int result = monitorStationService.insertMonitorStation(monitorStation);
        if (result > 0) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.error("新增失败");
    }

    /**
     * 修改监测站点
     */
    @ApiOperation("修改监测站点")
    @PreAuthorize("@ss.hasPermi('domain:station:edit')")
    @Log(title = "监测站点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Valid MonitorStation monitorStation) {
        if (monitorStation.getId() == null) {
            return AjaxResult.error("站点ID不能为空");
        }

        // 验证站点是否存在
        MonitorStation existStation = monitorStationService.selectMonitorStationById(monitorStation.getId());
        if (existStation == null) {
            return AjaxResult.error("监测站点不存在");
        }

        // 如果修改了站点编码，需要验证新编码是否已存在
        if (StringUtils.isNotEmpty(monitorStation.getCode()) &&
            !monitorStation.getCode().equals(existStation.getCode())) {
            MonitorStation codeQuery = new MonitorStation();
            codeQuery.setCode(monitorStation.getCode());
            List<MonitorStation> codeList = monitorStationService.selectMonitorStationList(codeQuery);
            if (codeList != null && !codeList.isEmpty()) {
                return AjaxResult.error("站点编码已存在");
            }
        }

        // 设置修改人
        monitorStation.setModifier(SecurityUtils.getUserId());

        int result = monitorStationService.updateMonitorStation(monitorStation);
        if (result > 0) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.error("修改失败");
    }

    /**
     * 删除监测站点
     */
    @ApiOperation("删除监测站点")
    @PreAuthorize("@ss.hasPermi('domain:station:remove')")
    @Log(title = "监测站点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable @NotEmpty(message = "删除ID不能为空") Long[] ids) {
        // 验证站点是否存在
        for (Long id : ids) {
            MonitorStation station = monitorStationService.selectMonitorStationById(id);
            if (station == null) {
                return AjaxResult.error("ID为 " + id + " 的监测站点不存在");
            }
        }

        int result = monitorStationService.deleteMonitorStationByIds(ids);
        if (result > 0) {
            return AjaxResult.success("删除成功，共删除 " + result + " 条记录");
        }
        return AjaxResult.error("删除失败");
    }

    /**
     * 批量更新站点状态
     */
    @ApiOperation("批量更新站点状态")
    @PreAuthorize("@ss.hasPermi('domain:station:edit')")
    @Log(title = "监测站点", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestParam @NotEmpty(message = "站点ID不能为空") Long[] ids,
                                   @RequestParam @NotNull(message = "状态不能为空") Integer state) {
        if (state != 0 && state != 1) {
            return AjaxResult.error("状态值只能为0（正常）或1（异常）");
        }

        int successCount = 0;
        for (Long id : ids) {
            MonitorStation station = monitorStationService.selectMonitorStationById(id);
            if (station != null) {
                station.setState(state);
                station.setModifier(SecurityUtils.getUserId());
                if (monitorStationService.updateMonitorStation(station) > 0) {
                    successCount++;
                }
            }
        }

        if (successCount > 0) {
            return AjaxResult.success("成功更新 " + successCount + " 条记录的状态");
        }
        return AjaxResult.error("状态更新失败");
    }

    /**
     * 获取站点统计信息
     */
    @ApiOperation("获取站点统计信息")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        MonitorStation query = new MonitorStation();
        query.setPageNum(null);
        query.setPageSize(null);
        List<MonitorStation> allStations = monitorStationService.selectMonitorStationList(query);

        long totalCount = allStations.size();
        long normalCount = allStations.stream().filter(s -> s.getState() != null && s.getState() == 0).count();
        long abnormalCount = allStations.stream().filter(s -> s.getState() != null && s.getState() == 1).count();
        long faultyCount = allStations.stream().filter(s -> s.getFaulty() != null && s.getFaulty() == 1).count();

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCount", totalCount);
        statistics.put("normalCount", normalCount);
        statistics.put("abnormalCount", abnormalCount);
        statistics.put("faultyCount", faultyCount);
        statistics.put("normalRate", totalCount > 0 ? String.format("%.2f", (double) normalCount / totalCount * 100) + "%" : "0%");

        return AjaxResult.success(statistics);
    }

    /**
     * 根据省市区查询站点
     */
    @ApiOperation("根据省市区查询站点")
    @GetMapping("/getByRegion")
    public AjaxResult getByRegion(@RequestParam(required = false) String province,
                                  @RequestParam(required = false) String city,
                                  @RequestParam(required = false) String district) {
        MonitorStation query = new MonitorStation();
        query.setProvince(province);
        query.setCity(city);
        query.setDistrict(district);
        query.setPageNum(null);
        query.setPageSize(null);

        List<MonitorStation> list = monitorStationService.selectMonitorStationList(query);
        return AjaxResult.success(list);
    }
}
