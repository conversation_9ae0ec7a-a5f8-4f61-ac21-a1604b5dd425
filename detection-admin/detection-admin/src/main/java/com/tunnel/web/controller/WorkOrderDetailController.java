package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.domain.WorkOrderDetail;
import com.tunnel.service.WorkOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 维护工单明细Controller
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/platform/workOrderDetail")
@Api(tags = "维护工单明细管理")
public class WorkOrderDetailController extends BaseController {
    @Autowired
    private WorkOrderDetailService workOrderDetailService;

    /**
     * 查询维护工单明细列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取维护工单明细列表", notes = "分页查询维护工单明细列表")
    @Operation(summary = "获取维护工单明细列表", description = "分页查询维护工单明细列表")
    public TableDataInfo list(WorkOrderDetail workOrderDetail) {
        startPage();
        List<WorkOrderDetail> list = workOrderDetailService.selectWorkOrderDetailList(workOrderDetail);
        return getDataTable(list);
    }

    /**
     * 根据工单ID查询工单明细列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:list')")
    @GetMapping("/listByOrderId/{orderId}")
    @ApiOperation(value = "根据工单ID查询明细", notes = "根据工单ID查询工单明细列表")
    @Operation(summary = "根据工单ID查询明细", description = "根据工单ID查询工单明细列表")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult listByOrderId(@PathVariable Long orderId) {
        List<WorkOrderDetail> list = workOrderDetailService.selectWorkOrderDetailListByOrderId(orderId);
        return success(list);
    }

    /**
     * 根据工单ID查询工单明细列表（按维护类型分组）
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:list')")
    @GetMapping("/listByOrderIdGrouped/{orderId}")
    @ApiOperation(value = "根据工单ID查询分组明细", notes = "根据工单ID查询工单明细列表（按维护类型分组）")
    @Operation(summary = "根据工单ID查询分组明细", description = "根据工单ID查询工单明细列表（按维护类型分组）")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult listByOrderIdGrouped(@PathVariable Long orderId) {
        Map<String, List<WorkOrderDetail>> groupedList = workOrderDetailService.selectWorkOrderDetailListByOrderIdGrouped(orderId);
        return success(groupedList);
    }

    /**
     * 根据工单ID获取工单详情展示数据
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:list')")
    @GetMapping("/displayData/{orderId}")
    @ApiOperation(value = "获取工单详情展示数据", notes = "根据工单ID获取工单详情展示数据（用于前端弹框）")
    @Operation(summary = "获取工单详情展示数据", description = "根据工单ID获取工单详情展示数据（用于前端弹框）")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getDisplayData(@PathVariable Long orderId) {
        Map<String, Object> displayData = workOrderDetailService.getWorkOrderDetailDisplayData(orderId);
        return success(displayData);
    }

    /**
     * 导出维护工单明细列表
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:export')")
    @Log(title = "维护工单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出维护工单明细", notes = "导出维护工单明细数据到Excel")
    @Operation(summary = "导出维护工单明细", description = "导出维护工单明细数据到Excel")
    public void export(HttpServletResponse response, WorkOrderDetail workOrderDetail) {
        workOrderDetailService.exportWorkOrderDetail(response, workOrderDetail);
    }

    /**
     * 获取维护工单明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取维护工单明细详细", notes = "根据ID获取维护工单明细详情")
    @Operation(summary = "获取维护工单明细详细", description = "根据ID获取维护工单明细详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(workOrderDetailService.selectWorkOrderDetailById(id));
    }

    /**
     * 新增维护工单明细
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:add')")
    @Log(title = "维护工单明细", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增维护工单明细", notes = "新增单条维护工单明细")
    @Operation(summary = "新增维护工单明细", description = "新增单条维护工单明细")
    public AjaxResult add(@Validated @RequestBody WorkOrderDetail workOrderDetail) {
        return toAjax(workOrderDetailService.insertWorkOrderDetail(workOrderDetail));
    }

    /**
     * 批量新增维护工单明细
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:add')")
    @Log(title = "维护工单明细", businessType = BusinessType.INSERT)
    @PostMapping("/batchAdd")
    @ApiOperation(value = "批量新增维护工单明细", notes = "批量新增维护工单明细")
    @Operation(summary = "批量新增维护工单明细", description = "批量新增维护工单明细")
    public AjaxResult batchAdd(@Validated @RequestBody List<WorkOrderDetail> workOrderDetailList) {
        return toAjax(workOrderDetailService.batchInsertWorkOrderDetail(workOrderDetailList));
    }

    /**
     * 修改维护工单明细
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:edit')")
    @Log(title = "维护工单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改维护工单明细", notes = "修改维护工单明细")
    @Operation(summary = "修改维护工单明细", description = "修改维护工单明细")
    public AjaxResult edit(@Validated @RequestBody WorkOrderDetail workOrderDetail) {
        return toAjax(workOrderDetailService.updateWorkOrderDetail(workOrderDetail));
    }

    /**
     * 删除维护工单明细
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:remove')")
    @Log(title = "维护工单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除维护工单明细", notes = "批量删除维护工单明细")
    @Operation(summary = "删除维护工单明细", description = "批量删除维护工单明细")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(workOrderDetailService.deleteWorkOrderDetailByIds(ids));
    }

    /**
     * 根据工单ID删除工单明细
     */
    @PreAuthorize("@ss.hasPermi('platform:workOrderDetail:remove')")
    @Log(title = "维护工单明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/byOrderId/{orderId}")
    @ApiOperation(value = "根据工单ID删除明细", notes = "根据工单ID删除所有工单明细")
    @Operation(summary = "根据工单ID删除明细", description = "根据工单ID删除所有工单明细")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult removeByOrderId(@PathVariable Long orderId) {
        return toAjax(workOrderDetailService.deleteWorkOrderDetailByOrderId(orderId));
    }

    /**
     * 根据工单ID统计明细数量
     */
    @GetMapping("/countByOrderId/{orderId}")
    @ApiOperation(value = "统计工单明细数量", notes = "根据工单ID统计明细数量")
    @Operation(summary = "统计工单明细数量", description = "根据工单ID统计明细数量")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult countByOrderId(@PathVariable Long orderId) {
        int count = workOrderDetailService.countWorkOrderDetailByOrderId(orderId);
        return success(count);
    }

    /**
     * 获取工单的维护类型列表
     */
    @GetMapping("/workTypeList/{orderId}")
    @ApiOperation(value = "获取维护类型列表", notes = "根据工单ID获取维护类型列表")
    @Operation(summary = "获取维护类型列表", description = "根据工单ID获取维护类型列表")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getWorkTypeList(@PathVariable Long orderId) {
        List<String> workTypeList = workOrderDetailService.selectWorkTypeListByOrderId(orderId);
        return success(workTypeList);
    }

    /**
     * 获取工单详情统计信息
     */
    @GetMapping("/statistics/{orderId}")
    @ApiOperation(value = "获取工单详情统计", notes = "根据工单ID获取工单详情统计信息")
    @Operation(summary = "获取工单详情统计", description = "根据工单ID获取工单详情统计信息")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getStatistics(@PathVariable Long orderId) {
        Map<String, Object> statistics = workOrderDetailService.getWorkOrderDetailStatistics(orderId);
        return success(statistics);
    }

    /**
     * 校验工单ID是否存在
     */
    @GetMapping("/checkOrderId/{orderId}")
    @ApiOperation(value = "校验工单ID", notes = "校验工单ID是否存在")
    @Operation(summary = "校验工单ID", description = "校验工单ID是否存在")
    @ApiImplicitParam(name = "orderId", value = "工单ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult checkOrderId(@PathVariable Long orderId) {
        boolean exists = workOrderDetailService.checkOrderIdExists(orderId);
        return success(exists);
    }

    /**
     * 下载工单明细导入模板
     */
    @PostMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载工单明细导入模板")
    @Operation(summary = "下载导入模板", description = "下载工单明细导入模板")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<WorkOrderDetail> util = new ExcelUtil<WorkOrderDetail>(WorkOrderDetail.class);
        util.importTemplateExcel(response, "维护工单明细数据");
    }
}
