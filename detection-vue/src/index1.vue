<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="路线编码" prop="roadCode">
        <el-input
          v-model="queryParams.roadCode"
          placeholder="请输入路线编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="桥梁名称" prop="bridgeName">
        <el-input
          v-model="queryParams.bridgeName"
          placeholder="请输入桥梁名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中心桩号" prop="centerPile">
        <el-input
          v-model="queryParams.centerPile"
          placeholder="请输入中心桩号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="风险等级" prop="riskLevel">
        <el-select v-model="queryParams.riskLevel" placeholder="请选择风险等级" clearable>
          <el-option label="一级" value="1" />
          <el-option label="二级" value="2" />
          <el-option label="三级" value="3" />
          <el-option label="四级" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急状态" prop="isUrgent">
        <el-select v-model="queryParams.isUrgent" placeholder="请选择紧急状态" clearable>
          <el-option label="否" value="0" />
          <el-option label="是" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['report:checkBridge:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['report:checkBridge:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['report:checkBridge:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['report:checkBridge:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['report:checkBridge:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Check"
          size="mini"
          :disabled="multiple"
          @click="handleAudit"
          v-hasPermi="['report:checkBridge:audit']"
        >审核</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="checkBridgeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="rowSelectable" />
      <el-table-column label="公司名称" align="center" prop="companyName" width="250"/>
      <el-table-column label="路线编码" align="center" prop="roadCode" />
      <el-table-column label="路线名称" align="center" prop="roadName" />
      <el-table-column label="桥梁名称" align="center" prop="bridgeName" />
      <el-table-column label="中心桩号" align="center" prop="centerPile" />
      <el-table-column label="风险等级" align="center" prop="riskLevel">
        <template #default="scope">
          <el-tag v-if="scope.row.riskLevel == '1'" type="success">一级</el-tag>
          <el-tag v-else-if="scope.row.riskLevel == '2'" type="warning">二级</el-tag>
          <el-tag v-else-if="scope.row.riskLevel == '3'" type="danger">三级</el-tag>
          <el-tag v-else-if="scope.row.riskLevel == '4'" type="danger" effect="dark">四级</el-tag>
          <span v-else>{{ scope.row.riskLevel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否紧急" align="center" prop="isUrgent">
        <template #default="scope">
          <el-tag v-if="scope.row.isUrgent == 1" type="danger">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="首次发现时间" align="center" prop="findTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.findTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维修截止时间" align="center" prop="repairEndTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.repairEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="上报照片" align="center" prop="reportPhotos" width="120">
        <template #default="scope">
          <div v-if="scope.row.reportPhotos">
            <el-button type="text" size="mini" @click="previewImages(scope.row.reportPhotos)">
              查看({{ scope.row.reportPhotos.split(',').length }}张)
            </el-button>
          </div>
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="checkStatus" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.checkStatus == 0" type="warning">待审核</el-tag>
          <el-tag v-else-if="scope.row.checkStatus == 1" type="success">审核通过</el-tag>
          <el-tag v-else-if="scope.row.checkStatus == 2" type="danger">不通过</el-tag>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['report:checkBridge:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['report:checkBridge:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改桥梁风险检查记录对话框 -->
    <el-dialog :title="title" v-model="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="选择路线" prop="roadId">
              <el-select v-model="form.roadId" placeholder="请选择路线" filterable clearable style="width: 100%">
                <el-option
                  v-for="road in roadList"
                  :key="road.id"
                  :label="`${road.companyName} - ${road.roadCode} (${road.roadName})`"
                  :value="road.id"
                >
                  <span style="float: left">{{ road.companyName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ road.roadCode }} - {{ road.roadName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="桥梁名称" prop="bridgeName">
              <el-input v-model="form.bridgeName" placeholder="请输入桥梁名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中心桩号" prop="centerPile">
              <el-input v-model="form.centerPile" placeholder="请输入中心桩号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="风险等级" prop="riskLevel">
              <el-select v-model="form.riskLevel" placeholder="请选择风险等级">
                <el-option label="一级" value="1" />
                <el-option label="二级" value="2" />
                <el-option label="三级" value="3" />
                <el-option label="四级" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否紧急" prop="isUrgent">
              <el-radio-group v-model="form.isUrgent">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="首次发现时间" prop="findTime">
              <el-date-picker
                v-model="form.findTime"
                type="date"
                placeholder="请选择首次发现时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维修截止时间" prop="repairEndTime">
              <el-date-picker
                v-model="form.repairEndTime"
                type="date"
                placeholder="请选择维修截止时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="风险因素" prop="riskFactor">
              <el-input v-model="form.riskFactor" type="textarea" placeholder="请输入风险因素" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="缺陷描述" prop="defectDesc">
              <el-input v-model="form.defectDesc" type="textarea" placeholder="请输入缺陷描述" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上报照片(最多3张)" prop="reportPhotos">
              <el-upload
                class="upload-demo"
                action="/dev-api/report/upload/uploadPicture"
                :on-success="reportPhotosUpload"
                :on-remove="reportPhotosRemove"
                :on-preview="handlePreview"
                :file-list="reportPhotosList"
                :headers="{'Authorization': 'Bearer ' + token}"
                list-type="picture-card"
                :limit="3">
                <el-icon><Plus /></el-icon>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <!-- 维修上报模块 -->
      <div v-if="form.id" style="margin-top: 20px;">
        <el-divider content-position="left">
          <span style="font-size: 16px; font-weight: bold; color: #409EFF;">
            <i class="el-icon-tools"></i> 维修上报信息
          </span>
        </el-divider>
        <RepairRecordForm 
          ref="repairRecordForm"
          :checkId="form.id" 
          :checkType="0" 
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保存检查记录</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="桥梁风险检查记录详情" v-model="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="公司名称">{{ viewForm.companyName }}</el-descriptions-item>
        <el-descriptions-item label="路线编码">{{ viewForm.roadCode }}</el-descriptions-item>
        <el-descriptions-item label="路线名称">{{ viewForm.roadName }}</el-descriptions-item>
        <el-descriptions-item label="桥梁名称">{{ viewForm.bridgeName }}</el-descriptions-item>
        <el-descriptions-item label="中心桩号">{{ viewForm.centerPile }}</el-descriptions-item>
        <el-descriptions-item label="风险等级">
          <el-tag v-if="viewForm.riskLevel == '1'" type="success">一级</el-tag>
          <el-tag v-else-if="viewForm.riskLevel == '2'" type="warning">二级</el-tag>
          <el-tag v-else-if="viewForm.riskLevel == '3'" type="danger">三级</el-tag>
          <el-tag v-else-if="viewForm.riskLevel == '4'" type="danger" effect="dark">四级</el-tag>
          <span v-else>{{ viewForm.riskLevel }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否紧急">
          <el-tag v-if="viewForm.isUrgent == 1" type="danger">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="首次发现时间">{{ parseTime(viewForm.findTime, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="维修截止时间">{{ parseTime(viewForm.repairEndTime, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="风险因素" :span="2">{{ viewForm.riskFactor }}</el-descriptions-item>
        <el-descriptions-item label="缺陷描述" :span="2">{{ viewForm.defectDesc }}</el-descriptions-item>
        <el-descriptions-item label="上报照片" :span="2">{{ viewForm.reportPhotos }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" v-model="imageVisible" width="800px">
      <div style="text-align: center;">
        <el-carousel v-if="imageUrls.length > 1" height="500px" indicator-position="outside">
          <el-carousel-item v-for="(url, index) in imageUrls" :key="index">
            <el-image :src="url" fit="contain" style="width: 100%; height: 500px;" />
          </el-carousel-item>
        </el-carousel>
        <el-image v-else-if="imageUrls.length === 1" :src="imageUrls[0]" fit="contain" style="max-width: 100%; max-height: 500px;" />
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核桥梁风险检查记录" v-model="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="100px">
        <el-form-item label="审核状态" prop="checkStatus">
          <el-radio-group v-model="auditForm.checkStatus" @change="handleStatusChange">
            <el-radio :label="1">审核通过</el-radio>
            <el-radio :label="2">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            placeholder="请输入审核备注"
            type="textarea"
            :rows="4"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAudit">确 定</el-button>
        <el-button @click="cancelAudit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCheckBridge, getCheckBridge, delCheckBridge, addCheckBridge, updateCheckBridge, auditCheckBridge } from "@/api/report/checkBridge";
import { listAllRoad } from "@/api/report/road";
import { getToken } from "@/utils/auth";
import {parseTime} from "../../../utils/ruoyi";
import RepairRecordForm from '@/components/RepairRecordForm/index.vue';
import { Plus } from '@element-plus/icons-vue';

export default {
  name: "CheckBridge",
  components: {
    RepairRecordForm,
    Plus
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 桥梁风险检查记录表格数据
      checkBridgeList: [],
      // 路线列表
      roadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查看详情弹出层
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        roadCode: null,
        bridgeName: null,
        centerPile: null,
        riskLevel: null,
        isUrgent: null,
      },
      // 表单参数
      form: {},
      // 查看表单
      viewForm: {},
      // 表单校验
      rules: {
        roadId: [
          { required: true, message: "请选择路线", trigger: "change" }
        ],
        bridgeName: [
          { required: true, message: "桥梁名称不能为空", trigger: "blur" }
        ],
        centerPile: [
          { required: true, message: "中心桩号不能为空", trigger: "blur" }
        ]
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + "/report/checkBridge/importData"
      },
      // 图片上传相关
      token: getToken(),
      reportPhotosList: [],
      imageUrls: [],
      imageVisible: false,
      // 审核相关
      auditOpen: false,
      auditForm: {
        ids: [],
        checkStatus: 1,
        remark: ''
      },
      auditRules: {
        checkStatus: [{ required: true, message: "审核状态不能为空", trigger: "change" }],
        remark: [
          {
            validator: (rule, value, callback) => {
              if (this.auditForm.checkStatus === 2 && (!value || value.trim() === '')) {
                callback(new Error('审核不通过时必须填写备注'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    // 检查当前用户是否为项目经理
  },
  created() {
    this.getList();
    this.getRoadList();
  },
  methods: {
    parseTime,
    /** 查询路线列表 */
    getRoadList() {
      listAllRoad({}).then(response => {
        this.roadList = response.rows;
      });
    },
    /** 查询桥梁风险检查记录列表 */
    getList() {
      this.loading = true;
      listCheckBridge(this.queryParams).then(response => {
        this.checkBridgeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        roadId: null,
        bridgeName: null,
        centerPile: null,
        riskFactor: null,
        defectDesc: null,
        reportPhotos: null,
        riskLevel: null,
        isUrgent: 0,
        repairEndTime: null,
        findTime: null,
        createTime: null,
        updateTime: null,
        creator: null,
        modifier: null
      };
      this.resetForm("form");
      // 重置维修记录表单
      this.$nextTick(() => {
        if (this.$refs.repairRecordForm) {
          this.$refs.repairRecordForm.resetForm();
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加桥梁风险检查记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCheckBridge(id).then(response => {
        this.form = response.data;
        // 处理多张图片
        if (this.form.reportPhotos) {
          const photos = this.form.reportPhotos.split(',');
          this.reportPhotosList = photos.map((photo, index) => ({
            name: `图片${index + 1}`,
            url: photo.trim()
          }));
        }
        this.open = true;
        this.title = "修改桥梁风险检查记录";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getCheckBridge(row.id).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCheckBridge(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCheckBridge(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除桥梁风险检查记录编号为"' + ids + '"的数据项？').then(function() {
        return delCheckBridge(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('report/checkBridge/export', {
        ...this.queryParams
      }, `桥梁风险检查记录_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "桥梁风险检查记录导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('report/checkBridge/importTemplate', {}, `桥梁风险检查记录导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 图片预览方法
    previewImages(urls) {
      if (urls) {
        this.imageUrls = urls.split(',').map(url => url.trim());
        this.imageVisible = true;
      }
    },
    // 上报照片上传成功处理
    reportPhotosUpload(response, file, fileList) {
      if (response.code === 200) {
        // 更新图片列表
        this.reportPhotosList = fileList.map(item => ({
          name: item.name,
          url: item.response ? item.response.msg : item.url
        }));
        
        // 更新表单数据（逗号分隔）
        this.form.reportPhotos = this.reportPhotosList.map(item => item.url).join(',');
      }
    },
    // 上报照片移除处理
    reportPhotosRemove(file, fileList) {
      // 更新图片列表
      this.reportPhotosList = fileList.map(item => ({
        name: item.name,
        url: item.response ? item.response.msg : item.url
      }));
      
      // 更新表单数据（逗号分隔）
      this.form.reportPhotos = this.reportPhotosList.length > 0 
        ? this.reportPhotosList.map(item => item.url).join(',') 
        : null;
    },
    // 图片预览处理
    handlePreview(file) {
      this.previewImages(file.url);
    },
    
    // 行选择控制 - 只有待审核状态的记录才能被选中
    rowSelectable(row, index) {
      return row.checkStatus === 0; // 只有待审核状态(0)才能选中
    },
    
    // 审核处理
    handleAudit() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要审核的记录");
        return;
      }
      this.auditForm.ids = [...this.ids];
      this.auditOpen = true;
    },
    
    // 审核状态变化处理
    handleStatusChange() {
      // 当状态变化时，重新验证备注字段
      this.$nextTick(() => {
        this.$refs.auditForm.validateField('remark');
      });
    },
    
    // 提交审核
    submitAudit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          auditCheckBridge(this.auditForm).then(response => {
            this.$modal.msgSuccess("审核成功");
            this.auditOpen = false;
            this.getList();
          });
        }
      });
    },
    
    // 取消审核
    cancelAudit() {
      this.auditOpen = false;
      this.auditForm = {
        ids: [],
        checkStatus: 1,
        remark: ''
      };
    }

  }
};
</script>

<style>
.upload-demo .el-upload--picture-card {
  width: 180px;
  height: 180px;
  line-height: 180px;
}
.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 180px;
  height: 180px;
}
</style> 