import request from '@/utils/request'

// 查询监测站点列表
export function listMonitorStation(query) {
  return request({
    url: '/detection/station/selectByPage',
    method: 'post',
    data: query
  })
}

// 查询监测站点详细
export function getMonitorStation(id) {
  return request({
    url: '/detection/station/' + id,
    method: 'get'
  })
}

// 新增监测站点
export function addMonitorStation(data) {
  return request({
    url: '/detection/station',
    method: 'post',
    data: data
  })
}

// 修改监测站点
export function updateMonitorStation(data) {
  return request({
    url: '/detection/station',
    method: 'put',
    data: data
  })
}

// 删除监测站点
export function delMonitorStation(id) {
  return request({
    url: '/detection/station/' + id,
    method: 'delete'
  })
}

// 导出监测站点
export function exportMonitorStation(query) {
  return request({
    url: '/detection/station/export',
    method: 'post',
    data: query
  })
}

// 查询所有监测站点列表（不分页）
export function listAllMonitorStation(query) {
  return request({
    url: '/detection/station/listAll',
    method: 'post',
    data: query
  })
}

// 根据站点编码查询监测站点
export function listAllForPage(data) {
  return request({
    url: '/detection/station/listAllForPage',
    method: 'post',
    data: data
  })
}

// 根据省市区查询监测站点
export function getMonitorStationByRegion(params) {
  return request({
    url: '/detection/station/getByRegion',
    method: 'get',
    params: params
  })
}

// 批量更新站点状态
export function batchUpdateStationStatus(data) {
  return request({
    url: '/detection/station/updateStatus',
    method: 'put',
    params: data
  })
}

// 获取站点统计信息
export function getStationStatistics() {
  return request({
    url: '/detection/station/statistics',
    method: 'get'
  })
}

// 导入监测站点
export function importMonitorStation(data) {
  return request({
    url: '/detection/station/import',
    method: 'post',
    data: data
  })
}
