import request from '@/utils/request'

// 查询服务区/收费站维护计划列表
export function listPlan(query) {
  return request({
    url: '/platform/plan/list',
    method: 'get',
    params: query
  })
}

// 查询服务区/收费站维护计划详细
export function getPlan(id) {
  return request({
    url: '/platform/plan/' + id,
    method: 'get'
  })
}

// 新增服务区/收费站维护计划
export function addPlan(data) {
  return request({
    url: '/platform/plan',
    method: 'post',
    data: data
  })
}

// 修改服务区/收费站维护计划
export function updatePlan(data) {
  return request({
    url: '/platform/plan',
    method: 'put',
    data: data
  })
}

// 删除服务区/收费站维护计划
export function delPlan(id) {
  return request({
    url: '/platform/plan/' + id,
    method: 'delete'
  })
}

// 导出服务区/收费站维护计划
export function exportPlan(query) {
  return request({
    url: '/platform/plan/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 根据站点ID查询维护计划列表
export function listPlanByStation(stationId) {
  return request({
    url: '/platform/plan/listByStation/' + stationId,
    method: 'get'
  })
}

// 校验站点ID是否存在
export function checkStationId(stationId) {
  return request({
    url: '/platform/plan/checkStationId/' + stationId,
    method: 'get'
  })
}

// 根据站点名称查询站点ID
export function getStationIdByName(stationName) {
  return request({
    url: '/platform/plan/getStationIdByName/' + stationName,
    method: 'get'
  })
}

// 导入维护计划数据
export function importPlan(data) {
  return request({
    url: '/platform/plan/importData',
    method: 'post',
    data: data
  })
}

// 导入维护计划数据（使用简化模板）
export function importPlanByVO(data) {
  return request({
    url: '/platform/plan/importDataByVO',
    method: 'post',
    data: data
  })
}

// 下载维护计划导入模板
export function downloadTemplate() {
  return request({
    url: '/platform/plan/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// 下载维护计划简化导入模板
export function downloadTemplateSimple() {
  return request({
    url: '/platform/plan/importTemplateSimple',
    method: 'post',
    responseType: 'blob'
  })
}
