import request from '@/utils/request'

// 查询维护工单列表
export function listWorkOrder(query) {
  return request({
    url: '/platform/workOrder/list',
    method: 'get',
    params: query
  })
}

// 查询维护工单详细
export function getWorkOrder(id) {
  return request({
    url: '/platform/workOrder/' + id,
    method: 'get'
  })
}

// 新增维护工单
export function addWorkOrder(data) {
  return request({
    url: '/platform/workOrder',
    method: 'post',
    data: data
  })
}

// 修改维护工单
export function updateWorkOrder(data) {
  return request({
    url: '/platform/workOrder',
    method: 'put',
    data: data
  })
}

// 删除维护工单
export function delWorkOrder(id) {
  return request({
    url: '/platform/workOrder/' + id,
    method: 'delete'
  })
}

// 导出维护工单
export function exportWorkOrder(query) {
  return request({
    url: '/platform/workOrder/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 根据计划ID查询工单列表
export function listWorkOrderByPlan(planId) {
  return request({
    url: '/platform/workOrder/listByPlan/' + planId,
    method: 'get'
  })
}

// 根据站点ID查询工单列表
export function listWorkOrderByStation(stationId) {
  return request({
    url: '/platform/workOrder/listByStation/' + stationId,
    method: 'get'
  })
}

// 完成工单
export function completeWorkOrder(id) {
  return request({
    url: '/platform/workOrder/complete/' + id,
    method: 'put'
  })
}

// 重新开始工单
export function restartWorkOrder(id) {
  return request({
    url: '/platform/workOrder/restart/' + id,
    method: 'put'
  })
}

// 批量更新工单状态
export function batchUpdateWorkOrderStatus(ids, status) {
  return request({
    url: '/platform/workOrder/batchUpdateStatus',
    method: 'put',
    params: {
      ids: ids,
      status: status
    }
  })
}

// 根据计划创建工单
export function createWorkOrderByPlan(planId) {
  return request({
    url: '/platform/workOrder/createByPlan/' + planId,
    method: 'post'
  })
}

// 校验计划ID是否存在
export function checkPlanId(planId) {
  return request({
    url: '/platform/workOrder/checkPlanId/' + planId,
    method: 'get'
  })
}

// 校验站点ID是否存在
export function checkStationId(stationId) {
  return request({
    url: '/platform/workOrder/checkStationId/' + stationId,
    method: 'get'
  })
}

// 根据计划ID和状态查询工单数量
export function countWorkOrderByPlanAndStatus(planId, status) {
  return request({
    url: '/platform/workOrder/countByPlanAndStatus',
    method: 'get',
    params: {
      planId: planId,
      status: status
    }
  })
}

// 下载工单导入模板
export function downloadWorkOrderTemplate() {
  return request({
    url: '/platform/workOrder/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

// ==================== 工单详情相关接口 ====================

// 查询维护工单明细列表
export function listWorkOrderDetail(query) {
  return request({
    url: '/platform/workOrderDetail/list',
    method: 'get',
    params: query
  })
}

// 根据工单ID查询工单明细列表
export function listWorkOrderDetailByOrderId(orderId) {
  return request({
    url: '/platform/workOrderDetail/listByOrderId/' + orderId,
    method: 'get'
  })
}

// 根据工单ID查询工单明细列表（按维护类型分组）
export function listWorkOrderDetailByOrderIdGrouped(orderId) {
  return request({
    url: '/platform/workOrderDetail/listByOrderIdGrouped/' + orderId,
    method: 'get'
  })
}

// 根据工单ID获取工单详情展示数据
export function getWorkOrderDetailDisplayData(orderId) {
  return request({
    url: '/platform/workOrderDetail/displayData/' + orderId,
    method: 'get'
  })
}

// 查询维护工单明细详细
export function getWorkOrderDetail(id) {
  return request({
    url: '/platform/workOrderDetail/' + id,
    method: 'get'
  })
}

// 新增维护工单明细
export function addWorkOrderDetail(data) {
  return request({
    url: '/platform/workOrderDetail',
    method: 'post',
    data: data
  })
}

// 批量新增维护工单明细
export function batchAddWorkOrderDetail(data) {
  return request({
    url: '/platform/workOrderDetail/batchAdd',
    method: 'post',
    data: data
  })
}

// 修改维护工单明细
export function updateWorkOrderDetail(data) {
  return request({
    url: '/platform/workOrderDetail',
    method: 'put',
    data: data
  })
}

// 删除维护工单明细
export function delWorkOrderDetail(id) {
  return request({
    url: '/platform/workOrderDetail/' + id,
    method: 'delete'
  })
}

// 根据工单ID删除工单明细
export function delWorkOrderDetailByOrderId(orderId) {
  return request({
    url: '/platform/workOrderDetail/byOrderId/' + orderId,
    method: 'delete'
  })
}

// 导出维护工单明细
export function exportWorkOrderDetail(query) {
  return request({
    url: '/platform/workOrderDetail/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 根据工单ID统计明细数量
export function countWorkOrderDetailByOrderId(orderId) {
  return request({
    url: '/platform/workOrderDetail/countByOrderId/' + orderId,
    method: 'get'
  })
}

// 获取工单的维护类型列表
export function getWorkTypeListByOrderId(orderId) {
  return request({
    url: '/platform/workOrderDetail/workTypeList/' + orderId,
    method: 'get'
  })
}

// 获取工单详情统计信息
export function getWorkOrderDetailStatistics(orderId) {
  return request({
    url: '/platform/workOrderDetail/statistics/' + orderId,
    method: 'get'
  })
}

// 校验工单ID是否存在
export function checkWorkOrderDetailOrderId(orderId) {
  return request({
    url: '/platform/workOrderDetail/checkOrderId/' + orderId,
    method: 'get'
  })
}

// 下载工单明细导入模板
export function downloadWorkOrderDetailTemplate() {
  return request({
    url: '/platform/workOrderDetail/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
