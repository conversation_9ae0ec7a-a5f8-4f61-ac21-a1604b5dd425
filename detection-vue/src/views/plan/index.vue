<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="站点名称" prop="stationNameOrCode">
        <el-input
          v-model="queryParams.stationNameOrCode"
          placeholder="请输入站点名称或编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地区" prop="district">
        <el-input
          v-model="queryParams.district"
          placeholder="请输入地区"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分公司" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入分公司"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="维护时间" prop="startTime">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['platform:scPlan:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['platform:scPlan:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:scPlan:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['platform:scPlan:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['platform:scPlan:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Upload"
          @click="handleImportSimple"
          v-hasPermi="['platform:scPlan:import']"
        >简化导入</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="planList" @selection-change="handleSelectionChange" stripe border>
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="站点编码" align="center" prop="stationCode" width="120" show-overflow-tooltip />
      <el-table-column label="站点名称" align="center" prop="stationName" width="140" show-overflow-tooltip />
      <el-table-column label="地区" align="center" prop="district" width="100" show-overflow-tooltip />
      <el-table-column label="分公司" align="center" prop="companyName" width="100" show-overflow-tooltip />
      <el-table-column label="开通情况" align="center" prop="openingStatus" width="90" />
      <el-table-column label="运维分类" align="center" prop="maintenanceCategory" width="90" />
      <el-table-column label="污水工艺" align="center" prop="sewageProcess" width="100" show-overflow-tooltip />
      <el-table-column label="设备型号" align="center" prop="equipmentModel" width="100" show-overflow-tooltip />
      <el-table-column label="设备数量" align="center" prop="equipmentQuantity" width="80" />
      <el-table-column label="处理规模" align="center" prop="processingScale" width="90">
        <template #default="scope">
          <span v-if="scope.row.processingScale">{{ scope.row.processingScale }}t/d</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="运维类型" align="center" prop="maintenanceType" width="90" />
      <el-table-column label="污染类型" align="center" prop="pollutionType" width="90" />
      <el-table-column label="维护开始时间" align="center" prop="startTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="维护截止时间" align="center" prop="endTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state" width="70">
        <template #default="scope">
          <el-tag :type="scope.row.state === 0 ? 'success' : 'danger'" size="small">
            {{ scope.row.state === 0 ? '正常' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="故障状态" align="center" prop="faulty" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.faulty === 0 ? 'success' : 'warning'" size="small">
            {{ scope.row.faulty === 0 ? '正常' : '故障' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="120" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:plan:edit']"
          >
            修改
          </el-button>
          <el-button
            link
            type="danger"
            size="small"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:plan:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改维护计划对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="planRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="站点" prop="stationId">
          <el-select v-model="form.stationId" placeholder="请选择站点" filterable clearable style="width: 100%">
            <el-option
              v-for="station in stationList"
              :key="station.code"
              :label="`${station.name}`"
              :value="station.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="维护开始时间" prop="startTime">
          <el-date-picker
            v-model="form.startTime"
            type="datetime"
            placeholder="选择维护开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="维护截止时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="datetime"
            placeholder="选择维护截止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的维护计划数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 简化导入对话框 -->
    <el-dialog :title="uploadSimple.title" v-model="uploadSimple.open" width="400px" append-to-body>
      <el-upload
        ref="uploadSimpleRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="uploadSimple.headers"
        :action="uploadSimple.url + '?updateSupport=' + uploadSimple.updateSupport"
        :disabled="uploadSimple.isUploading"
        :on-progress="handleFileUploadProgressSimple"
        :on-success="handleFileSuccessSimple"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="uploadSimple.updateSupport" />是否更新已经存在的维护计划数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。支持"维护时间、服务区、备注"三列格式。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplateSimple">下载简化模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileFormSimple">确 定</el-button>
          <el-button @click="uploadSimple.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Plan">
import { listPlan, getPlan, delPlan, addPlan, updatePlan } from "@/api/platform/plan";
import { listAllForPage } from "@/api/platform/monitorStation";
import { parseTime } from "@/utils/ruoyi";
import { getCurrentInstance, reactive, ref, toRefs, onMounted } from "vue";
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();

const planList = ref([]);
const stationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

// 表单引用
const planRef = ref();
const queryRef = ref();
const uploadRef = ref();
const uploadSimpleRef = ref();



// 上传参数
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/platform/plan/importData"
});

// 简化上传参数
const uploadSimple = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/platform/plan/importDataByVO"
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    stationNameOrCode: null,
    district: null,
    companyName: null,
    startTime: null,
    endTime: null,
    remark: null
  },
  rules: {
    stationId: [
      { required: true, message: "站点不能为空", trigger: "change" }
    ],
    startTime: [
      { required: true, message: "维护开始时间不能为空", trigger: "blur" }
    ],
    endTime: [
      { required: true, message: "维护截止时间不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询维护计划列表 */
function getList() {
  loading.value = true;
  listPlan(proxy.addDateRange(queryParams.value, dateRange.value, "startTime")).then(response => {
    planList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    stationId: null,
    startTime: null,
    endTime: null,
    remark: null
  };
  if (planRef.value) {
    planRef.value.resetFields();
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加维护计划";
  loadStationList();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getPlan(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改维护计划";
    loadStationList();
  });
}

/** 提交按钮 */
function submitForm() {
  planRef.value.validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updatePlan(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPlan(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除维护计划编号为"' + _ids + '"的数据项？').then(function() {
    return delPlan(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/plan/export', {
    ...queryParams.value
  }, `维护计划_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "维护计划导入";
  upload.open = true;
}

/** 简化导入按钮操作 */
function handleImportSimple() {
  uploadSimple.title = "维护计划简化导入";
  uploadSimple.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download('platform/plan/importTemplate', {}, `维护计划导入模板_${new Date().getTime()}.xlsx`)
}

/** 下载简化模板操作 */
function importTemplateSimple() {
  proxy.download('platform/plan/importTemplateSimple', {}, `维护计划简化导入模板_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true;
}

/** 文件上传成功处理 */
function handleFileSuccess(response, file, fileList) {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value.clearFiles();
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value.submit();
}

/** 简化导入文件上传中处理 */
function handleFileUploadProgressSimple(event, file, fileList) {
  uploadSimple.isUploading = true;
}

/** 简化导入文件上传成功处理 */
function handleFileSuccessSimple(response, file, fileList) {
  uploadSimple.open = false;
  uploadSimple.isUploading = false;
  uploadSimpleRef.value.clearFiles();
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

/** 提交简化导入上传文件 */
function submitFileFormSimple() {
  uploadSimpleRef.value.submit();
}

/** 加载站点列表 */
function loadStationList() {
  console.log('开始加载站点列表...');
  listAllForPage({
    pageNum: 1,
    pageSize: 1000  // 获取足够多的站点数据
  }).then(response => {
    console.log('API响应:', response);
    // 根据你提供的数据结构，数据在response.data中
    stationList.value = response.data || [];
    console.log('站点列表数据:', stationList.value);
    console.log('站点列表长度:', stationList.value.length);
  }).catch(error => {
    console.error('加载站点列表失败:', error);
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-table {
  font-size: 12px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-tag {
  font-size: 11px;
}
</style>
