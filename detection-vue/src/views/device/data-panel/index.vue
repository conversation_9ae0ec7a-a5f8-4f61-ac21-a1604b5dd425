<template>
  <div class="app-container" v-loading="loading">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-width="auto"
        :inline="true"
      >
        <el-form-item label="网关" prop="gatewayId">
          <el-select
            v-model="form.gatewayId"
            @change="handelGatewayChange"
            placeholder="请选择网关"
          >
            <el-option
              v-for="item in gatewayArr"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <!-- :disabled-date="disabledDate" -->
        <el-form-item label="日期范围" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="datetimerange"
            range-separator="-"
            :disabled-date="disabledDate"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <br />
    <el-card>
      <div class="real-time--content">
        <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="上报" name="1"></el-tab-pane>
          <el-tab-pane label="状态" name="2"></el-tab-pane>
          <el-tab-pane label="报警" name="3"></el-tab-pane>
        </el-tabs>
        <div class="ml-4">
          <el-radio-group class="" v-model="viewType" @change="submitForm">
            <el-radio-button label="图表模式" value="图表模式" />
            <el-radio-button label="列表模式" value="列表模式" />
          </el-radio-group>
          &nbsp; &nbsp;
          <el-button type="success" @click="showSelectView()">
            显示内容
          </el-button>
        </div>

        <EchartsView
          ref="refEchartsView"
          v-show="viewType == '图表模式'"
          :config="fieldsByTypeArr"
        />
        <TableList
          ref="refTableList"
          :fieldsByTypeArr="fieldsByTypeArr"
          v-show="viewType == '列表模式'"
        />
        <select-view ref="refSelectView" :fieldsByTypeArr="fieldsByTypeArr" />
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup name="data-panel">
import { ref, defineAsyncComponent, onMounted } from "vue";
import {
  listAll,
  selectTimeRange,
  getCheckFieldsByType,
} from "@/api/call-police/process.js";
import { getStartOfDayFormatted, getCurrentTimeFormatted } from "@/utils/time";
import CONSTANTS from "@/common/constants";
import type { TabsPaneContext, FormInstance } from "element-plus";
const EchartsView = defineAsyncComponent(
  () => import("./compoments/ecahrtsVies/index.vue")
);
const TableList = defineAsyncComponent(
  () => import("./compoments/tableList/index.vue")
);
const SelectView = defineAsyncComponent(
  () => import("./compoments/select-view/index.vue")
);
const viewType = ref("图表模式");
const form = ref<any>({
  gatewayId: "",
  chosenDate: [],
});
const classifications = ref(CONSTANTS.classifications);
const filteredList = ref<any[]>([]);
const activeName = ref<any>("1");
const gatewayArr = ref<any[]>([]);
const startTime = ref(getStartOfDayFormatted());
const endTime = ref(getCurrentTimeFormatted());
const loading = ref(false);
const disabledDate = (time: Date) => {
  const start = new Date(startTime.value);
  const end = new Date(endTime.value);
  return time.getTime() < start.getTime() || time.getTime() > end.getTime();
};
const _listAll = async () => {
  try {
    const { data } = await listAll({});
    gatewayArr.value = data;
    if (!!gatewayArr.value.length) {
      form.value.gatewayId = gatewayArr.value[0].code;
      await _getCheckFieldsByType();
      await getSelectTimeRange();
    }
  } catch (err) {
    console.log(err);
  }
};
const serviceAreaChanged = (val: any) => {
  console.log(val);
};
const handleClick = async (tab: TabsPaneContext, event: Event) => {
  activeName.value = tab.props.name;
  await _getCheckFieldsByType();
  await submitForm();
};
const refTableList = ref<any>(null);
const refEchartsView = ref<any>(null);
const ruleFormRef = ref<TabsPaneContext>(null);
const submitForm = async () => {
  loading.value = true;
  let objs = {
    type: activeName.value,
    gatewayId: form.value.gatewayId,
    startTime: form.value.chosenDate?.[0],
    endTime: form.value.chosenDate?.[1],
  };
  viewType.value === "列表模式" && (await refTableList.value?.getList(objs));
  viewType.value === "图表模式" && (await refEchartsView.value?.getList(objs));
  setTimeout(() => {
    loading.value = false;
  }, 500);
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const refSelectView = ref<any>(null);
const showSelectView = () => {
  refSelectView.value.openDialog();
};
const handelGatewayChange = (val: any) => {
  getSelectTimeRange();
};
const getSelectTimeRange = async () => {
  try {
    const { data } = await selectTimeRange({
      type: activeName.value,
      gatewayId: form.value.gatewayId,
    });
    startTime.value = data?.startTime;
    endTime.value = data?.endTime;
    form.value.chosenDate = [startTime.value, endTime.value];
    await submitForm();
  } catch (err) {
    console.log(err);
  }
};
const fieldsByTypeArr = ref([]);
const _getCheckFieldsByType = async () => {
  try {
    const { data } = await getCheckFieldsByType({
      type: activeName.value,
    });
    fieldsByTypeArr.value = data;
  } catch (err) {
    console.log(err);
  }
};
onMounted( () => {
  _listAll();
  
  // await submitForm()
});
</script>
<style lang="scss" scoped>
.real-time--content {
  position: relative;
  // padding-top: 40px;
  .ml-4 {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
  }
}
</style>