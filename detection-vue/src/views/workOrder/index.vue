<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="站点名称" prop="stationNameOrCode">
        <el-input
          v-model="queryParams.stationNameOrCode"
          placeholder="请输入站点名称或编码"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择工单状态" clearable>
          <el-option label="进行中" value="0" />
          <el-option label="已完成" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="工单类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择工单类型" clearable>
          <el-option label="普通" value="0" />
          <el-option label="紧急工单" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="地区" prop="district">
        <el-input
          v-model="queryParams.district"
          placeholder="请输入地区"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分公司" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入分公司"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="createTimeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['platform:workOrder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['platform:workOrder:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:workOrder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['platform:workOrder:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchComplete"
          v-hasPermi="['platform:workOrder:edit']"
        >批量完成</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="workOrderList" @selection-change="handleSelectionChange" stripe border>
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="工单ID" align="center" prop="id" width="80" />
      <el-table-column label="站点编码" align="center" prop="stationCode" width="120" show-overflow-tooltip />
      <el-table-column label="站点名称" align="center" prop="stationName" width="140" show-overflow-tooltip />
      <el-table-column label="地区" align="center" prop="district" width="100" show-overflow-tooltip />
      <el-table-column label="分公司" align="center" prop="companyName" width="100" show-overflow-tooltip />
      <el-table-column label="计划开始时间" align="center" prop="planStartTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.planStartTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划截止时间" align="center" prop="planEndTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.planEndTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工单状态" align="center" prop="status" width="90">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'warning' : 'success'" size="small">
            {{ scope.row.status === 0 ? '进行中' : '已完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工单类型" align="center" prop="type" width="90">
        <template #default="scope">
          <el-tag :type="scope.row.type === 0 ? 'info' : 'danger'" size="small">
            {{ scope.row.type === 0 ? '普通' : '紧急工单' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" align="center" width="220" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="info"
            size="small"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['platform:workOrderDetail:list']"
          >
            查看详情
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:workOrder:edit']"
          >
            修改
          </el-button>
          <el-button
            v-if="scope.row.status === 0"
            link
            type="success"
            size="small"
            @click="handleComplete(scope.row)"
            v-hasPermi="['platform:workOrder:edit']"
          >
            完成
          </el-button>
          <el-button
            v-if="scope.row.status === 1"
            link
            type="warning"
            size="small"
            @click="handleRestart(scope.row)"
            v-hasPermi="['platform:workOrder:edit']"
          >
            重新开始
          </el-button>
          <el-button
            link
            type="danger"
            size="small"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:workOrder:remove']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工单对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="workOrderRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="计划" prop="planId">
          <el-select v-model="form.planId" placeholder="请选择计划" filterable clearable style="width: 100%" @change="onPlanChange">
            <el-option
              v-for="plan in planList"
              :key="plan.id"
              :label="`${plan.stationName} (${parseTime(plan.startTime, '{y}-{m}-{d}')} - ${parseTime(plan.endTime, '{y}-{m}-{d}')})`"
              :value="plan.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="站点" prop="stationId">
          <el-select v-model="form.stationId" placeholder="请选择站点" filterable clearable style="width: 100%" :disabled="!!form.planId">
            <el-option
              v-for="station in stationList"
              :key="station.id"
              :label="`${station.name} (${station.code})`"
              :value="station.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工单状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="0">进行中</el-radio>
            <el-radio :label="1">已完成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="工单类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="0">普通</el-radio>
            <el-radio :label="1">紧急工单</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工单详情弹框 -->
    <WorkOrderDetailDialog
      v-model="detailDialogVisible"
      :orderId="currentOrderId"
      @edit="handleEditFromDetail"
      @addDetail="handleAddDetailFromDetail"
    />
  </div>
</template>

<script setup name="WorkOrder">
import { listWorkOrder, getWorkOrder, delWorkOrder, addWorkOrder, updateWorkOrder, completeWorkOrder, restartWorkOrder, batchUpdateWorkOrderStatus } from "@/api/platform/workOrder";
import { listPlan } from "@/api/platform/plan";
import { listAllForPage } from "@/api/platform/monitorStation";
import { parseTime } from "@/utils/ruoyi";
import { getCurrentInstance, reactive, ref, toRefs, onMounted } from "vue";
import { getToken } from "@/utils/auth";
import WorkOrderDetailDialog from './components/WorkOrderDetailDialog.vue';

const { proxy } = getCurrentInstance();

const workOrderList = ref([]);
const planList = ref([]);
const stationList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const createTimeRange = ref([]);

// 详情弹框相关
const detailDialogVisible = ref(false);
const currentOrderId = ref(null);

// 表单引用
const workOrderRef = ref();
const queryRef = ref();

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    stationNameOrCode: null,
    status: null,
    type: null,
    district: null,
    companyName: null,
    createTimeBegin: null,
    createTimeEnd: null,
    remark: null
  },
  rules: {
    planId: [
      { required: true, message: "计划不能为空", trigger: "change" }
    ],
    stationId: [
      { required: true, message: "站点不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "工单状态不能为空", trigger: "change" }
    ],
    type: [
      { required: true, message: "工单类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询工单列表 */
function getList() {
  loading.value = true;
  listWorkOrder(proxy.addDateRange(queryParams.value, createTimeRange.value, "createTime")).then(response => {
    workOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    planId: null,
    stationId: null,
    status: 0,
    type: 0,
    remark: null
  };
  if (workOrderRef.value) {
    workOrderRef.value.resetFields();
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  createTimeRange.value = [];
  if (queryRef.value) {
    queryRef.value.resetFields();
  }
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加工单";
  loadPlanList();
  loadStationList();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getWorkOrder(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改工单";
    loadPlanList();
    loadStationList();
  });
}

/** 提交按钮 */
function submitForm() {
  workOrderRef.value.validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateWorkOrder(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWorkOrder(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除工单编号为"' + _ids + '"的数据项？').then(function() {
    return delWorkOrder(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('platform/workOrder/export', {
    ...queryParams.value
  }, `维护工单_${new Date().getTime()}.xlsx`)
}

/** 完成工单 */
function handleComplete(row) {
  proxy.$modal.confirm('是否确认完成工单"' + row.id + '"？').then(function() {
    return completeWorkOrder(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("工单已完成");
  }).catch(() => {});
}

/** 重新开始工单 */
function handleRestart(row) {
  proxy.$modal.confirm('是否确认重新开始工单"' + row.id + '"？').then(function() {
    return restartWorkOrder(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("工单已重新开始");
  }).catch(() => {});
}

/** 批量完成工单 */
function handleBatchComplete() {
  proxy.$modal.confirm('是否确认批量完成选中的工单？').then(function() {
    return batchUpdateWorkOrderStatus(ids.value, 1);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("批量完成成功");
  }).catch(() => {});
}

/** 加载计划列表 */
function loadPlanList() {
  listPlan({
    pageNum: 1,
    pageSize: 1000
  }).then(response => {
    planList.value = response.rows || [];
  });
}

/** 加载站点列表 */
function loadStationList() {
  listAllForPage({
    pageNum: 1,
    pageSize: 1000
  }).then(response => {
    stationList.value = response.data || [];
  });
}

/** 计划变化时自动设置站点 */
function onPlanChange(planId) {
  if (planId) {
    const selectedPlan = planList.value.find(plan => plan.id === planId);
    if (selectedPlan) {
      // 根据计划中的stationId（字符串）找到对应的站点ID（数字）
      const station = stationList.value.find(s => s.code === selectedPlan.stationId);
      if (station) {
        form.value.stationId = station.id;
      }
    }
  } else {
    form.value.stationId = null;
  }
}

/** 查看工单详情 */
function handleViewDetail(row) {
  currentOrderId.value = row.id;
  detailDialogVisible.value = true;
}

/** 从详情弹框编辑工单 */
function handleEditFromDetail(workOrder) {
  detailDialogVisible.value = false;
  // 延迟一下再打开编辑弹框，避免冲突
  setTimeout(() => {
    form.value = { ...workOrder };
    open.value = true;
    title.value = "修改工单";
    loadPlanList();
    loadStationList();
  }, 100);
}

/** 从详情弹框添加详情 */
function handleAddDetailFromDetail(orderId) {
  // 这里可以打开添加工单详情的弹框
  proxy.$modal.msgInfo("添加工单详情功能待开发");
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
.el-table {
  font-size: 12px;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

.el-button + .el-button {
  margin-left: 8px;
}

.el-tag {
  font-size: 11px;
}
</style>
