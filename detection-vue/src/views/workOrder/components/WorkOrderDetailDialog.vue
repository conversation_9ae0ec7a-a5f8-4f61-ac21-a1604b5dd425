<template>
  <el-dialog
    title="工单详情"
    v-model="visible"
    width="1400px"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
    class="work-order-detail-dialog"
  >
    <div class="work-order-detail-container" v-loading="loading">
      <div class="detail-content">
        <!-- 左侧工单信息和详情 -->
        <div class="left-content">
          <!-- 工单基本信息 -->
          <div class="work-order-info">
            <div class="info-header">
              <h3 class="info-title">
                <i class="el-icon-document"></i>
                工单详情
              </h3>
              <el-tag :type="workOrderInfo.type === 0 ? 'info' : 'danger'" size="medium">
                {{ workOrderInfo.type === 0 ? '普通工单' : '紧急工单' }}
              </el-tag>
            </div>

            <div class="info-grid">
              <div class="info-row">
                <div class="info-item">
                  <label>分公司</label>
                  <span>{{ workOrderInfo.companyName || '威宁' }}</span>
                </div>
                <div class="info-item">
                  <label>工单</label>
                  <span>{{ workOrderInfo.id || '1' }}</span>
                </div>
                <div class="info-item">
                  <label>地区</label>
                  <span>{{ workOrderInfo.district || '通城县' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>开通情况</label>
                  <span>{{ workOrderInfo.openingStatus || '已开通' }}</span>
                </div>
                <div class="info-item">
                  <label>污水处理工艺</label>
                  <span>{{ workOrderInfo.sewageProcess || 'MABR' }}</span>
                </div>
                <div class="info-item">
                  <label>设备型号</label>
                  <span>{{ workOrderInfo.equipmentModel || 'S-IV型' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>处理规模(t/d)</label>
                  <span>{{ workOrderInfo.processingScale || '400' }}</span>
                </div>
                <div class="info-item">
                  <label>部门分类</label>
                  <span>{{ workOrderInfo.maintenanceCategory || '乙类' }}</span>
                </div>
                <div class="info-item">
                  <label>工单类型</label>
                  <span>{{ workOrderInfo.type === 0 ? '普通工单' : '紧急工单' }}</span>
                </div>
              </div>

              <div class="info-row">
                <div class="info-item">
                  <label>维护日期</label>
                  <span>{{ parseTime(workOrderInfo.planStartTime, '{y}.{m}.{d}') || '2025.09.02' }}</span>
                </div>
                <div class="info-item">
                  <label>维护人</label>
                  <span>{{ workOrderInfo.creator || '1' }}</span>
                </div>
                <div class="info-item">
                  <label>备注</label>
                  <span>{{ workOrderInfo.remark || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 工单详情列表 -->
          <div class="work-order-details">
            <div v-for="(details, workType) in groupedDetails" :key="workType" class="work-type-group">
              <h3 class="work-type-title">
                <i class="el-icon-folder-opened"></i>
                {{ workType }}
              </h3>

              <!-- 按work_title进一步分组 -->
              <div v-for="(titleGroup, workTitle) in groupDetailsByTitle(details)" :key="workTitle" class="work-title-group">
                <h4 class="work-title-subtitle">
                  <i class="el-icon-menu"></i>
                  {{ getWorkTitleName(workTitle) }}
                </h4>
                <div class="detail-grid">
                  <div
                    v-for="detail in titleGroup"
                    :key="detail.id"
                    class="detail-item"
                  >
                    <div class="detail-card">
                      <div class="detail-image-full" @click="handleImagePreview(detail.picUrl)">
                        <img
                          v-if="detail.picUrl"
                          :src="getImageUrl(detail.picUrl)"
                          alt="工单图片"
                          class="preview-image"
                        />
                        <div v-else class="no-image">
                          <i class="el-icon-picture-outline"></i>
                          <span>暂无图片</span>
                        </div>
                        <div v-if="detail.picUrl" class="image-overlay">
                          <i class="el-icon-zoom-in"></i>
                          <span>点击预览</span>
                        </div>
                      </div>
                      <div class="detail-info-minimal">
                        <p class="detail-remark" v-if="detail.remark" :title="detail.remark">
                          {{ detail.remark }}
                        </p>
                        <div class="detail-meta">
                          <p class="detail-location" v-if="detail.longitude && detail.latitude">
                            <i class="el-icon-location"></i>
                            {{ detail.longitude }}, {{ detail.latitude }}
                          </p>
                          <p class="detail-time">
                            <i class="el-icon-time"></i>
                            {{ parseTime(detail.createTime, '{y}.{m}.{d}') }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 补充空白占位符，确保每行3个 -->
                  <div
                    v-for="i in getPlaceholderCount(titleGroup.length)"
                    :key="'placeholder-' + workTitle + '-' + i"
                    class="detail-item add-placeholder"
                    @click="handleAddDetail(workType, workTitle)"
                  >
                    <div class="add-placeholder-content">
                      <i class="el-icon-plus"></i>
                      <span>点击上传</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧时间线 -->
        <div class="right-content">
          <div class="timeline-container">
            <h3 class="timeline-title">工单时间线</h3>
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in timelineData"
                :key="index"
                :timestamp="item.timestamp"
                :type="item.type"
                :icon="item.icon"
                placement="top"
              >
                <div class="timeline-content">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.description }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <el-button type="primary" @click="handleEdit" v-hasPermi="['platform:workOrder:edit']">编辑工单</el-button>
        <el-button type="success" @click="handleAddDetail" v-hasPermi="['platform:workOrderDetail:add']">添加详情</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 图片预览弹窗 -->
  <el-dialog
    title="图片预览"
    v-model="imagePreviewVisible"
    width="800px"
    append-to-body
    destroy-on-close
    class="image-preview-dialog"
  >
    <div class="image-preview-container">
      <img :src="previewImageUrl" alt="预览图片" class="preview-image-large" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="imagePreviewVisible = false">关 闭</el-button>
        <el-button type="primary" @click="downloadImage">下载图片</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { getWorkOrderDetailDisplayData } from '@/api/platform/workOrder'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit', 'addDetail'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const workOrderInfo = ref({})
const groupedDetails = ref({})
const statistics = ref({})

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 时间线数据
const timelineData = ref([
  {
    timestamp: '2025.1.18 09:43:22',
    title: '已完成',
    description: '维护完成',
    type: 'success',
    icon: 'el-icon-check'
  },
  {
    timestamp: '2025.1.18 09:40:22',
    title: '进行中',
    description: '设备维护中',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '进行中',
    description: '污水处理设备维护检修维护及设备',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '进行中',
    description: '设备维护检修维护',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '已开始',
    description: '开始维护',
    type: 'warning',
    icon: 'el-icon-time'
  }
])

// 监听orderId变化，加载数据
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && visible.value) {
    loadWorkOrderDetail()
  }
}, { immediate: true })

// 监听弹框显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.orderId) {
    loadWorkOrderDetail()
  }
})

// 加载工单详情数据
const loadWorkOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    const response = await getWorkOrderDetailDisplayData(props.orderId)
    const data = response.data
    
    workOrderInfo.value = data.workOrder || {}
    groupedDetails.value = data.groupedDetails || {}
    statistics.value = data.statistics || {}
    
    // 生成时间线数据（基于工单信息）
    generateTimelineData()
  } catch (error) {
    console.error('加载工单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 生成时间线数据
const generateTimelineData = () => {
  const timeline = []
  const workOrder = workOrderInfo.value
  
  if (workOrder.status === 1) {
    timeline.push({
      timestamp: parseTime(workOrder.updateTime, '{y}.{m}.{d} {h}:{i}:{s}'),
      title: '已完成',
      description: '工单已完成',
      type: 'success',
      icon: 'el-icon-check'
    })
  }
  
  if (workOrder.status === 0) {
    timeline.push({
      timestamp: parseTime(new Date(), '{y}.{m}.{d} {h}:{i}:{s}'),
      title: '进行中',
      description: '工单正在处理中',
      type: 'primary',
      icon: 'el-icon-loading'
    })
  }
  
  timeline.push({
    timestamp: parseTime(workOrder.createTime, '{y}.{m}.{d} {h}:{i}:{s}'),
    title: '已创建',
    description: '工单已创建',
    type: 'info',
    icon: 'el-icon-plus'
  })
  
  timelineData.value = timeline
}

// 获取图片URL
const getImageUrl = (picUrl) => {
  // 这里根据实际情况处理图片URL
  if (typeof picUrl === 'string') {
    return picUrl
  }
  return '/default-image.jpg' // 默认图片
}

// 按work_title进一步分组
const groupDetailsByTitle = (details) => {
  const grouped = {}
  details.forEach(detail => {
    const title = detail.workTitle
    if (!grouped[title]) {
      grouped[title] = []
    }
    grouped[title].push(detail)
  })
  return grouped
}

// 获取工作标题名称
const getWorkTitleName = (workTitle) => {
  // 这里可以根据workTitle的值返回对应的名称
  const titleMap = {
    1: '进出水水质检测',
    2: '生化池指标检测',
    3: '格栅维护保养',
    4: '提升泵维护保养',
    5: '设备检查',
    6: '设备维护',
    7: '设备清洁',
    8: '设备修理'
  }
  return titleMap[workTitle] || `维护项目${workTitle}`
}

// 计算占位符数量（确保每行3个）
const getPlaceholderCount = (currentCount) => {
  const remainder = currentCount % 3
  return remainder === 0 ? 0 : 3 - remainder
}

// 关闭弹框
const handleClose = () => {
  visible.value = false
}

// 编辑工单
const handleEdit = () => {
  emit('edit', workOrderInfo.value)
}

// 添加详情
const handleAddDetail = (workType, workTitle) => {
  emit('addDetail', {
    orderId: props.orderId,
    workType: workType,
    workTitle: workTitle
  })
}

// 图片预览
const handleImagePreview = (imageUrl) => {
  if (imageUrl) {
    previewImageUrl.value = getImageUrl(imageUrl)
    imagePreviewVisible.value = true
  }
}

// 下载图片
const downloadImage = () => {
  if (previewImageUrl.value) {
    const link = document.createElement('a')
    link.href = previewImageUrl.value
    link.download = `工单图片_${new Date().getTime()}.jpg`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}
</script>

<style scoped>
/* 弹窗样式 */
:deep(.work-order-detail-dialog .el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.work-order-detail-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

:deep(.work-order-detail-dialog .el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

:deep(.work-order-detail-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.work-order-detail-container {
  height: 650px;
  overflow: hidden;
}

.detail-content {
  display: flex;
  height: 100%;
  gap: 24px;
}

.left-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 12px;
}

.right-content {
  width: 320px;
  border-left: 2px solid #e8f4fd;
  padding-left: 24px;
  background: #fafbfc;
  border-radius: 8px;
}

/* 工单信息样式 */
.work-order-info {
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #e8f4fd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8f4fd;
}

.info-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-title i {
  color: #409eff;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  color: #666;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item span {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 500;
  padding: 4px 0;
}

/* 工单详情样式 */
.work-order-details {
  margin-top: 8px;
}

.work-type-group {
  margin-bottom: 32px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}

.work-type-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #409eff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.work-type-title i {
  color: #409eff;
}

.work-title-group {
  margin-bottom: 28px;
}

.work-title-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 16px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #67c23a;
  display: flex;
  align-items: center;
  gap: 6px;
}

.work-title-subtitle i {
  color: #67c23a;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.detail-item {
  border: 1px solid #e8f4fd;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 300px;
  background: white;
}

.detail-item:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-4px);
  border-color: #409eff;
}

.detail-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-image-full {
  height: 240px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.detail-image-full img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.detail-image-full:hover img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.detail-image-full:hover .image-overlay {
  opacity: 1;
}

.image-overlay i {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-overlay span {
  font-size: 12px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #c0c4cc;
}

.no-image i {
  font-size: 32px;
  margin-bottom: 8px;
}

.no-image span {
  font-size: 12px;
}

.detail-info-minimal {
  padding: 8px 12px;
  background: #fff;
  border-radius: 0 0 12px 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 60px;
}

.detail-remark {
  color: #606266;
  font-size: 12px;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.detail-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-location,
.detail-time {
  color: #909399;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 3px;
}

.detail-location i,
.detail-time i {
  font-size: 9px;
}

/* 添加占位符样式 */
.add-placeholder {
  border: 2px dashed #d9ecff;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.add-placeholder:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.add-placeholder-content {
  height: 298px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s ease;
}

.add-placeholder:hover .add-placeholder-content {
  color: #409eff;
}

.add-placeholder-content i {
  font-size: 32px;
  margin-bottom: 12px;
}

.add-placeholder-content span {
  font-size: 13px;
  font-weight: 500;
}

/* 时间线样式 */
.timeline-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 24px;
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #e8f4fd;
}

:deep(.el-timeline) {
  padding-left: 0;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 24px;
}

:deep(.el-timeline-item__node) {
  width: 12px;
  height: 12px;
  left: -6px;
}

:deep(.el-timeline-item__tail) {
  left: -1px;
  border-left: 2px solid #e4e7ed;
}

.timeline-content h4 {
  margin: 0 0 6px 0;
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
}

.timeline-content p {
  margin: 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
}

/* 图片预览弹窗样式 */
:deep(.image-preview-dialog .el-dialog) {
  border-radius: 12px;
}

:deep(.image-preview-dialog .el-dialog__header) {
  background: #2c3e50;
  color: white;
  padding: 16px 24px;
}

:deep(.image-preview-dialog .el-dialog__title) {
  color: white;
  font-size: 16px;
}

:deep(.image-preview-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.preview-image-large {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* 滚动条样式 */
.left-content::-webkit-scrollbar,
.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.left-content::-webkit-scrollbar-track,
.timeline-container::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.left-content::-webkit-scrollbar-thumb,
.timeline-container::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.left-content::-webkit-scrollbar-thumb:hover,
.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .right-content {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
  }

  .detail-content {
    flex-direction: column;
  }

  .right-content {
    width: 100%;
    border-left: none;
    border-top: 2px solid #e8f4fd;
    padding-left: 0;
    padding-top: 20px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
