<template>
  <el-dialog
    title="工单详情"
    v-model="visible"
    width="1200px"
    :before-close="handleClose"
    append-to-body
    destroy-on-close
  >
    <div class="work-order-detail-container" v-loading="loading">
      <div class="detail-content">
        <!-- 左侧工单信息和详情 -->
        <div class="left-content">
          <!-- 工单基本信息 -->
          <div class="work-order-info">
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>分公司</label>
                  <span>{{ workOrderInfo.companyName || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>工单</label>
                  <span>{{ workOrderInfo.id || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>地区</label>
                  <span>{{ workOrderInfo.district || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>开通情况</label>
                  <span>{{ workOrderInfo.openingStatus || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>污水处理工艺</label>
                  <span>{{ workOrderInfo.sewageProcess || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>设备型号</label>
                  <span>{{ workOrderInfo.equipmentModel || '-' }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>处理规模(t/d)</label>
                  <span>{{ workOrderInfo.processingScale || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>部门分类</label>
                  <span>{{ workOrderInfo.maintenanceCategory || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>工单类型</label>
                  <el-tag :type="workOrderInfo.type === 0 ? 'info' : 'danger'" size="small">
                    {{ workOrderInfo.type === 0 ? '普通' : '紧急工单' }}
                  </el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="info-item">
                  <label>维护日期</label>
                  <span>{{ parseTime(workOrderInfo.planStartTime, '{y}.{m}.{d}') || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>维护人</label>
                  <span>{{ workOrderInfo.creator || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="info-item">
                  <label>备注</label>
                  <span>{{ workOrderInfo.remark || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 工单详情列表 -->
          <div class="work-order-details">
            <div v-for="(details, workType) in groupedDetails" :key="workType" class="work-type-group">
              <h3 class="work-type-title">{{ workType }}</h3>

              <!-- 按work_title进一步分组 -->
              <div v-for="(titleGroup, workTitle) in groupDetailsByTitle(details)" :key="workTitle" class="work-title-group">
                <h4 class="work-title-subtitle">{{ getWorkTitleName(workTitle) }}</h4>
                <div class="detail-grid">
                  <div
                    v-for="(detail, index) in titleGroup"
                    :key="detail.id"
                    class="detail-item"
                  >
                    <div class="detail-card">
                      <div class="detail-image-full">
                        <img v-if="detail.picUrl" :src="getImageUrl(detail.picUrl)" alt="工单图片" />
                        <div v-else class="no-image">
                          <i class="el-icon-picture"></i>
                          <span>暂无图片</span>
                        </div>
                      </div>
                      <div class="detail-info-minimal">
                        <p class="detail-remark" v-if="detail.remark">{{ detail.remark }}</p>
                        <p class="detail-location" v-if="detail.longitude && detail.latitude">
                          {{ detail.longitude }}, {{ detail.latitude }}
                        </p>
                        <p class="detail-time">{{ parseTime(detail.createTime, '{y}.{m}.{d}') }}</p>
                      </div>
                    </div>
                  </div>

                  <!-- 补充空白占位符，确保每行3个 -->
                  <div
                    v-for="i in getPlaceholderCount(titleGroup.length)"
                    :key="'placeholder-' + workTitle + '-' + i"
                    class="detail-item add-placeholder"
                    @click="handleAddDetail(workType, workTitle)"
                  >
                    <div class="add-placeholder-content">
                      <i class="el-icon-plus"></i>
                      <span>点击上传</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧时间线 -->
        <div class="right-content">
          <div class="timeline-container">
            <h3 class="timeline-title">工单时间线</h3>
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in timelineData"
                :key="index"
                :timestamp="item.timestamp"
                :type="item.type"
                :icon="item.icon"
                placement="top"
              >
                <div class="timeline-content">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.description }}</p>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <el-button type="primary" @click="handleEdit" v-hasPermi="['platform:workOrder:edit']">编辑工单</el-button>
        <el-button type="success" @click="handleAddDetail" v-hasPermi="['platform:workOrderDetail:add']">添加详情</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { getWorkOrderDetailDisplayData } from '@/api/platform/workOrder'
import { parseTime } from '@/utils/ruoyi'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit', 'addDetail'])

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const loading = ref(false)
const workOrderInfo = ref({})
const groupedDetails = ref({})
const statistics = ref({})

// 时间线数据
const timelineData = ref([
  {
    timestamp: '2025.1.18 09:43:22',
    title: '已完成',
    description: '维护完成',
    type: 'success',
    icon: 'el-icon-check'
  },
  {
    timestamp: '2025.1.18 09:40:22',
    title: '进行中',
    description: '设备维护中',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '进行中',
    description: '污水处理设备维护检修维护及设备',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '进行中',
    description: '设备维护检修维护',
    type: 'primary',
    icon: 'el-icon-loading'
  },
  {
    timestamp: '2025.1.18 09:36:22',
    title: '已开始',
    description: '开始维护',
    type: 'warning',
    icon: 'el-icon-time'
  }
])

// 监听orderId变化，加载数据
watch(() => props.orderId, (newOrderId) => {
  if (newOrderId && visible.value) {
    loadWorkOrderDetail()
  }
}, { immediate: true })

// 监听弹框显示状态
watch(visible, (newVisible) => {
  if (newVisible && props.orderId) {
    loadWorkOrderDetail()
  }
})

// 加载工单详情数据
const loadWorkOrderDetail = async () => {
  if (!props.orderId) return
  
  loading.value = true
  try {
    const response = await getWorkOrderDetailDisplayData(props.orderId)
    const data = response.data
    
    workOrderInfo.value = data.workOrder || {}
    groupedDetails.value = data.groupedDetails || {}
    statistics.value = data.statistics || {}
    
    // 生成时间线数据（基于工单信息）
    generateTimelineData()
  } catch (error) {
    console.error('加载工单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 生成时间线数据
const generateTimelineData = () => {
  const timeline = []
  const workOrder = workOrderInfo.value
  
  if (workOrder.status === 1) {
    timeline.push({
      timestamp: parseTime(workOrder.updateTime, '{y}.{m}.{d} {h}:{i}:{s}'),
      title: '已完成',
      description: '工单已完成',
      type: 'success',
      icon: 'el-icon-check'
    })
  }
  
  if (workOrder.status === 0) {
    timeline.push({
      timestamp: parseTime(new Date(), '{y}.{m}.{d} {h}:{i}:{s}'),
      title: '进行中',
      description: '工单正在处理中',
      type: 'primary',
      icon: 'el-icon-loading'
    })
  }
  
  timeline.push({
    timestamp: parseTime(workOrder.createTime, '{y}.{m}.{d} {h}:{i}:{s}'),
    title: '已创建',
    description: '工单已创建',
    type: 'info',
    icon: 'el-icon-plus'
  })
  
  timelineData.value = timeline
}

// 获取图片URL
const getImageUrl = (picUrl) => {
  // 这里根据实际情况处理图片URL
  if (typeof picUrl === 'string') {
    return picUrl
  }
  return '/default-image.jpg' // 默认图片
}

// 按work_title进一步分组
const groupDetailsByTitle = (details) => {
  const grouped = {}
  details.forEach(detail => {
    const title = detail.workTitle
    if (!grouped[title]) {
      grouped[title] = []
    }
    grouped[title].push(detail)
  })
  return grouped
}

// 获取工作标题名称
const getWorkTitleName = (workTitle) => {
  // 这里可以根据workTitle的值返回对应的名称
  const titleMap = {
    1: '进出水水质检测',
    2: '生化池指标检测',
    3: '格栅维护保养',
    4: '提升泵维护保养',
    5: '设备检查',
    6: '设备维护',
    7: '设备清洁',
    8: '设备修理'
  }
  return titleMap[workTitle] || `维护项目${workTitle}`
}

// 计算占位符数量（确保每行3个）
const getPlaceholderCount = (currentCount) => {
  const remainder = currentCount % 3
  return remainder === 0 ? 0 : 3 - remainder
}

// 关闭弹框
const handleClose = () => {
  visible.value = false
}

// 编辑工单
const handleEdit = () => {
  emit('edit', workOrderInfo.value)
}

// 添加详情
const handleAddDetail = (workType, workTitle) => {
  emit('addDetail', {
    orderId: props.orderId,
    workType: workType,
    workTitle: workTitle
  })
}
</script>

<style scoped>
.work-order-detail-container {
  height: 600px;
  overflow: hidden;
}

.detail-content {
  display: flex;
  height: 100%;
  gap: 20px;
}

.left-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
}

.right-content {
  width: 300px;
  border-left: 1px solid #ebeef5;
  padding-left: 20px;
}

/* 工单信息样式 */
.work-order-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.info-item label {
  width: 80px;
  color: #666;
  font-size: 14px;
  margin-right: 10px;
  flex-shrink: 0;
}

.info-item span {
  color: #333;
  font-size: 14px;
}

/* 工单详情样式 */
.work-type-group {
  margin-bottom: 30px;
}

.work-type-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.work-title-group {
  margin-bottom: 25px;
}

.work-title-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  padding-left: 10px;
  border-left: 3px solid #67c23a;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.detail-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  height: 280px;
}

.detail-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.detail-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-image-full {
  height: 200px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.detail-image-full img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.no-image i {
  font-size: 24px;
  margin-bottom: 5px;
}

.detail-info-minimal {
  padding: 8px 10px;
  background: #fff;
  border-radius: 0 0 8px 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.detail-remark {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-location {
  color: #999;
  font-size: 11px;
  margin-bottom: 2px;
}

.detail-time {
  color: #999;
  font-size: 11px;
  margin: 0;
}

/* 添加占位符样式 */
.add-placeholder {
  border: 2px dashed #dcdfe6;
  background: #fafafa;
}

.add-placeholder:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.add-placeholder-content {
  height: 278px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  cursor: pointer;
}

.add-placeholder-content i {
  font-size: 24px;
  margin-bottom: 8px;
}

/* 时间线样式 */
.timeline-container {
  height: 100%;
  overflow-y: auto;
}

.timeline-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.timeline-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #333;
}

.timeline-content p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

/* 滚动条样式 */
.left-content::-webkit-scrollbar,
.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.left-content::-webkit-scrollbar-track,
.timeline-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.left-content::-webkit-scrollbar-thumb,
.timeline-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.left-content::-webkit-scrollbar-thumb:hover,
.timeline-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
